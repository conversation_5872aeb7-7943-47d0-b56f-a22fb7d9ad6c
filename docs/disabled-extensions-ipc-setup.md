# 禁用插件状态导入 - IPC通道设置

## 🎯 架构概述

为了解决浏览器环境无法直接访问SQLite数据库的问题，我们采用了VSCode标准的IPC通道模式：

- **主进程 (Node.js)**: 运行SQLite读取服务
- **渲染进程 (Browser)**: 通过IPC通道调用主进程服务
- **IPC通道**: 负责跨进程通信

## 📁 文件结构

```
src/vs/platform/settingsSync/
├── common/
│   ├── settingsSync.ts                          # 服务接口定义
│   ├── settingsSyncService.ts                   # 通用服务实现
│   └── disabledExtensionsReaderChannel.ts       # IPC通道定义
└── node/
    └── settingsSyncService.ts                   # Node.js专用服务实现
```

## 🔧 实现细节

### 1. 服务接口 (`common/settingsSync.ts`)

```typescript
export const IDisabledExtensionsReaderService = createDecorator<IDisabledExtensionsReaderService>('disabledExtensionsReaderService');

export interface IDisabledExtensionsReaderService {
    readonly _serviceBrand: undefined;
    readDisabledExtensions(ideType: string): Promise<IExtensionIdentifier[]>;
}
```

### 2. IPC通道 (`common/disabledExtensionsReaderChannel.ts`)

```typescript
// 服务端 - 在主进程中运行
export class DisabledExtensionsReaderChannel implements IServerChannel {
    constructor(private service: IDisabledExtensionsReaderService) { }
    
    call(_: unknown, command: string, arg?: any): Promise<any> {
        switch (command) {
            case 'readDisabledExtensions': return this.service.readDisabledExtensions(arg);
        }
        throw new Error(`Call not found: ${command}`);
    }
}

// 客户端 - 在渲染进程中运行
export class DisabledExtensionsReaderChannelClient implements IDisabledExtensionsReaderService {
    constructor(private channel: IChannel) { }
    
    readDisabledExtensions(ideType: string): Promise<IExtensionIdentifier[]> {
        return this.channel.call('readDisabledExtensions', ideType);
    }
}
```

### 3. Node.js服务实现 (`node/settingsSyncService.ts`)

```typescript
export class DisabledExtensionsReaderService implements IDisabledExtensionsReaderService {
    async readDisabledExtensions(ideType: string): Promise<IExtensionIdentifier[]> {
        // SQLite数据库读取逻辑
        const sqlite3 = await importAMDNodeModule<typeof import('@vscode/sqlite3')>('@vscode/sqlite3', 'lib/sqlite3.js');
        // ... 数据库操作
    }
}
```

## 🚀 注册步骤

### 步骤1: 在主进程中注册服务

需要在主进程的服务注册文件中添加：

```typescript
// 在主进程服务注册文件中 (例如: src/vs/code/electron-main/main.ts)
import { DisabledExtensionsReaderService } from 'vs/platform/settingsSync/node/settingsSyncService';
import { DisabledExtensionsReaderChannel, DISABLED_EXTENSIONS_READER_CHANNEL_NAME } from 'vs/platform/settingsSync/common/disabledExtensionsReaderChannel';

// 注册服务
const disabledExtensionsReaderService = new DisabledExtensionsReaderService(logService, fileService);

// 注册IPC通道
const disabledExtensionsReaderChannel = new DisabledExtensionsReaderChannel(disabledExtensionsReaderService);
mainProcessService.registerChannel(DISABLED_EXTENSIONS_READER_CHANNEL_NAME, disabledExtensionsReaderChannel);
```

### 步骤2: 在渲染进程中注册客户端

需要在渲染进程的服务注册文件中添加：

```typescript
// 在渲染进程服务注册文件中 (例如: src/vs/workbench/services/...)
import { DisabledExtensionsReaderChannelClient, DISABLED_EXTENSIONS_READER_CHANNEL_NAME } from 'vs/platform/settingsSync/common/disabledExtensionsReaderChannel';
import { IDisabledExtensionsReaderService } from 'vs/platform/settingsSync/common/settingsSync';

// 获取IPC通道
const channel = mainProcessService.getChannel(DISABLED_EXTENSIONS_READER_CHANNEL_NAME);

// 创建客户端
const disabledExtensionsReaderService = new DisabledExtensionsReaderChannelClient(channel);

// 注册到依赖注入容器
serviceCollection.set(IDisabledExtensionsReaderService, disabledExtensionsReaderService);
```

## 📋 具体注册位置

### 主进程注册
建议在以下文件中注册：
- `src/vs/code/electron-main/main.ts`
- 或者专门的服务注册文件

### 渲染进程注册
建议在以下文件中注册：
- `src/vs/workbench/services/...` 相关的服务注册文件
- 或者在 `settingsSync.contribution.ts` 中注册

## 🔍 调试和验证

### 验证IPC通道注册
1. 检查主进程日志，确认通道已注册
2. 检查渲染进程日志，确认客户端已创建
3. 测试IPC调用是否正常工作

### 测试SQLite读取
1. 确保源IDE存在并有禁用插件
2. 检查SQLite数据库文件路径是否正确
3. 验证数据库查询结果

## ⚠️ 注意事项

1. **依赖注入顺序**: 确保在使用前已正确注册服务
2. **错误处理**: IPC调用可能失败，需要适当的错误处理
3. **性能考虑**: SQLite操作在主进程中进行，避免阻塞渲染进程
4. **安全性**: 验证传入的IDE类型参数

## 🎯 下一步

1. 找到合适的服务注册位置
2. 添加服务注册代码
3. 测试IPC通道是否正常工作
4. 验证禁用插件导入功能

这种架构确保了：
- ✅ 浏览器环境兼容性
- ✅ Node.js功能的完整利用
- ✅ 符合VSCode的架构模式
- ✅ 良好的错误处理和调试能力
