# 设置同步与禁用插件状态导入示例

## 🎯 功能演示

这个示例展示了如何使用新的禁用插件状态导入功能，该功能已集成到现有的设置同步系统中。

## 📋 使用场景

### 场景1: 从VSCode导入完整配置

用户从VSCode迁移到当前IDE，希望保持所有设置和插件状态一致。

**操作步骤:**
1. 打开命令面板 (`Cmd/Ctrl + Shift + P`)
2. 输入并选择 `Import VSCode Settings`
3. 确认导入操作
4. 等待导入完成

**导入内容:**
- ✅ 用户设置 (settings.json)
- ✅ 快捷键配置 (keybindings.json)  
- ✅ 插件列表和文件
- ✅ MCP设置
- ✅ **禁用插件状态** (新功能!)

### 场景2: 从Cursor导入配置

**操作步骤:**
1. 打开命令面板
2. 输入并选择 `Import Cursor Settings`
3. 确认导入
4. 完成导入

## 🔍 导入过程详解

### 1. 确认对话框
```
Import settings preview
This will overwrite your current IDE settings and cannot be undone. Do you want to continue?
[Import] [Cancel]
```

### 2. 导入进度通知
```
ℹ️ Importing VSCode settings, extensions and disabled extensions state...
```

### 3. 具体导入步骤
```
📖 Reading settings...
📖 Reading disabled extensions...
📝 Writing settings...
📝 Applying disabled extensions...
📦 Importing extensions...
✅ Settings imported successfully
✅ Disabled extensions imported successfully
```

### 4. 完成通知
```
✅ Import from VSCode Complete.
Settings, Keybindings, MCP settings and disabled extensions were imported successfully.
[OK] [View Failed Details]
```

## 📊 导入结果示例

### 成功导入
假设VSCode中有以下禁用插件：
- `ms-python.python` (Python扩展)
- `vscodevim.vim` (Vim扩展)
- `ms-vscode.vscode-typescript-next` (TypeScript扩展)

导入后，这些插件在当前IDE中也会被自动禁用，用户无需手动操作。

### 日志输出
```
[INFO] Reading disabled extensions from VSCode...
[INFO] Found 3 disabled extensions in VSCode storage
[INFO] Applying disabled extensions to current IDE...
[INFO] Disabled extension: ms-python.python
[INFO] Disabled extension: vscodevim.vim  
[INFO] Extension ms-vscode.vscode-typescript-next is already disabled
[INFO] Successfully imported 3 disabled extensions from VSCode
```

## 🛠️ 开发者使用

### 程序化调用
```typescript
import { ISettingsSyncService } from 'vs/platform/settingsSync/common/settingsSync';

// 在服务中使用
class MyService {
    constructor(
        @ISettingsSyncService private settingsSyncService: ISettingsSyncService
    ) {}

    async importFromVSCode() {
        try {
            // 导入设置
            await this.settingsSyncService.importSettings('Code');
            
            // 导入MCP设置
            await this.settingsSyncService.importMcpSettings('Code');
            
            // 导入禁用插件状态
            await this.settingsSyncService.importDisabledExtensions('Code');
            
            console.log('All imports completed successfully');
        } catch (error) {
            console.error('Import failed:', error);
        }
    }
}
```

### 自定义导入逻辑
```typescript
// 只导入禁用插件状态
async function importOnlyDisabledExtensions(sourceIDE: 'Code' | 'Cursor') {
    const settingsSyncService = accessor.get(ISettingsSyncService);
    
    try {
        const success = await settingsSyncService.importDisabledExtensions(sourceIDE);
        if (success) {
            console.log(`Successfully imported disabled extensions from ${sourceIDE}`);
        }
    } catch (error) {
        console.error(`Failed to import disabled extensions from ${sourceIDE}:`, error);
    }
}
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 源IDE数据库不存在
**现象**: 日志显示 "Storage database not found"
**原因**: 源IDE从未使用过或数据被清理
**解决**: 正常现象，导入会跳过此步骤

#### 2. 权限问题
**现象**: 导入失败，提示权限错误
**解决**: 确保当前用户有读取源IDE数据目录的权限

#### 3. 数据库被锁定
**现象**: 导入时提示数据库忙碌
**解决**: 关闭源IDE后重试

#### 4. 部分插件导入失败
**现象**: 通知显示部分插件导入失败
**解决**: 查看详细错误信息，手动处理失败的插件

### 调试技巧

#### 查看详细日志
1. 打开开发者工具 (`Cmd/Ctrl + Shift + I`)
2. 切换到 Console 标签
3. 查找包含 "disabled extensions" 的日志

#### 验证导入结果
1. 打开插件管理器
2. 查看禁用插件列表
3. 对比源IDE的禁用状态

## 📈 性能考虑

### 导入时间
- 设置文件: < 1秒
- 禁用插件状态: < 2秒  
- 插件文件: 取决于插件数量和大小

### 内存使用
- SQLite数据库读取: 最小内存占用
- 异步处理: 不阻塞UI线程

## 🔮 未来扩展

### 计划中的功能
1. **增量导入**: 只导入变更的禁用状态
2. **双向同步**: 支持导出禁用状态到其他IDE
3. **批量操作**: 支持批量启用/禁用导入的插件
4. **配置过滤**: 允许用户选择要导入的插件类别

### 扩展示例
```typescript
// 未来可能的API
interface IAdvancedSettingsSyncService extends ISettingsSyncService {
    // 增量导入
    importDisabledExtensionsIncremental(sourceIDE: string, lastSyncTime: Date): Promise<boolean>;
    
    // 导出功能
    exportDisabledExtensions(targetPath: string): Promise<boolean>;
    
    // 选择性导入
    importDisabledExtensionsSelective(sourceIDE: string, extensionIds: string[]): Promise<boolean>;
}
```

## 📚 相关资源

- [VSCode设置同步文档](./vscode-settings-import.md)
- [插件管理API文档](../src/vs/platform/extensionManagement/README.md)
- [存储服务文档](../src/vs/platform/storage/README.md)
