/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as assert from 'assert';
import { DisposableStore } from '../../../../base/common/lifecycle.js';
import { TestInstantiationService } from '../../../../platform/instantiation/test/common/instantiationServiceMock.js';
import { ILogService, NullLogService } from '../../../../platform/log/common/log.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { FileService } from '../../../../platform/files/common/fileService.js';
import { InMemoryFileSystemProvider } from '../../../../platform/files/common/inMemoryFileSystemProvider.js';
import { Schemas } from '../../../../base/common/network.js';
import { URI } from '../../../../base/common/uri.js';
import { IEnvironmentService } from '../../../../platform/environment/common/environment.js';
import { mock } from '../../../../base/test/common/mock.js';
import { SettingsSyncService } from '../../../../platform/settingsSync/common/settingsSyncService.js';
import { ISettingsSyncProgressService } from '../../../../platform/settingsSync/common/settingsSyncProgress.js';
import { IGlobalExtensionEnablementService, IExtensionIdentifier } from '../../../../platform/extensionManagement/common/extensionManagement.js';
import { VSCodeIdeType } from '../../../../platform/environment/common/environmentService.js';

suite('Disabled Extensions Import Tests', () => {
	let disposables: DisposableStore;
	let instantiationService: TestInstantiationService;
	let settingsSyncService: SettingsSyncService;
	let mockGlobalExtensionEnablementService: IGlobalExtensionEnablementService;
	let mockEnvironmentService: IEnvironmentService;
	let mockProgressService: ISettingsSyncProgressService;

	setup(() => {
		disposables = new DisposableStore();
		instantiationService = new TestInstantiationService();

		// Setup file service
		const fileService = new FileService(new NullLogService());
		const fileSystemProvider = new InMemoryFileSystemProvider();
		fileService.registerProvider(Schemas.file, fileSystemProvider);
		instantiationService.stub(IFileService, fileService);
		instantiationService.stub(ILogService, new NullLogService());

		// Mock environment service
		mockEnvironmentService = new class extends mock<IEnvironmentService>() {
			override userRoamingDataHome = URI.file('/test/user');
		};
		instantiationService.stub(IEnvironmentService, mockEnvironmentService);

		// Mock progress service
		mockProgressService = new class extends mock<ISettingsSyncProgressService>() {
			override report() { }
		};
		instantiationService.stub(ISettingsSyncProgressService, mockProgressService);

		// Mock global extension enablement service
		const mockDisabledExtensions: IExtensionIdentifier[] = [];
		mockGlobalExtensionEnablementService = new class extends mock<IGlobalExtensionEnablementService>() {
			override getDisabledExtensions() {
				return mockDisabledExtensions;
			}
			override async disableExtension(extension: IExtensionIdentifier, source?: string) {
				mockDisabledExtensions.push(extension);
				return true;
			}
			override async enableExtension(extension: IExtensionIdentifier, source?: string) {
				const index = mockDisabledExtensions.findIndex(ext => ext.id === extension.id);
				if (index !== -1) {
					mockDisabledExtensions.splice(index, 1);
					return true;
				}
				return false;
			}
		};
		instantiationService.stub(IGlobalExtensionEnablementService, mockGlobalExtensionEnablementService);

		settingsSyncService = instantiationService.createInstance(SettingsSyncService);
	});

	teardown(() => {
		disposables.dispose();
	});

	test('should handle missing storage database gracefully', async () => {
		// Test importing from a non-existent source
		const result = await settingsSyncService.importDisabledExtensions('Code' as VSCodeIdeType);
		
		// Should return true even if no database exists
		assert.strictEqual(result, true);
	});

	test('should apply disabled extensions correctly', async () => {
		// This test would require setting up a mock SQLite database
		// For now, we test the service instantiation and basic method existence
		assert.ok(settingsSyncService);
		assert.ok(typeof settingsSyncService.importDisabledExtensions === 'function');
	});

	test('should not duplicate already disabled extensions', async () => {
		// Pre-disable an extension
		await mockGlobalExtensionEnablementService.disableExtension({ id: 'test.extension' });
		
		const initialCount = mockGlobalExtensionEnablementService.getDisabledExtensions().length;
		assert.strictEqual(initialCount, 1);
		
		// The actual import would need a mock database, but we can test the service structure
		assert.ok(settingsSyncService.importDisabledExtensions);
	});

	test('should handle invalid JSON data gracefully', async () => {
		// This would test the JSON parsing error handling
		// The actual implementation would need a mock database with invalid data
		const result = await settingsSyncService.importDisabledExtensions('Cursor' as VSCodeIdeType);
		assert.strictEqual(result, true);
	});
});

/**
 * Integration test helper for creating test databases
 * This would be used in more comprehensive tests
 */
export class TestDatabaseHelper {
	static async createTestStorageDatabase(path: string, disabledExtensions: IExtensionIdentifier[]): Promise<void> {
		// This would create a test SQLite database with the specified disabled extensions
		// Implementation would use the same SQLite logic as the main service
		console.log(`Would create test database at ${path} with extensions:`, disabledExtensions);
	}

	static async verifyDatabaseContent(path: string, expectedExtensions: IExtensionIdentifier[]): Promise<boolean> {
		// This would verify the database contains the expected disabled extensions
		console.log(`Would verify database at ${path} contains:`, expectedExtensions);
		return true;
	}
}

/**
 * Mock VSCodePathUtils for testing
 */
export class MockVSCodePathUtils {
	constructor(private nameShort: VSCodeIdeType) { }

	getUserDataPath(): string {
		return `/test/${this.nameShort.toLowerCase()}`;
	}

	getStorageDbPath(): string {
		return `${this.getUserDataPath()}/User/globalStorage/state.vscdb`;
	}
}
