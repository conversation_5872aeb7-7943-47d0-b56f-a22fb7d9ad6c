import { IEditCodeService, ShowDiffParams, AcceptDiffParams, RejectDiffParams, DiffAcceptedEvent, DiffRejectedEvent, DiffArea, Diff, UpdateDiffParams, StreamingState } from '../common/editCode.js';
import { IEditorService } from '../../../../workbench/services/editor/common/editorService.js';
import { Emitter } from '../../../../base/common/event.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';
import { ITextFileService } from '../../../services/textfile/common/textfiles.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { IEditorWorkerService } from '../../../../editor/common/services/editorWorker.js';
import { ICodeEditor, IViewZone, IContentWidget, ContentWidgetPositionPreference } from '../../../../editor/browser/editorBrowser.js';
import { IEditorDecorationsCollection } from '../../../../editor/common/editorCommon.js';
import { Range } from '../../../../editor/common/core/range.js';
import { ChatEditingModifiedDocumentEntry } from '../../../contrib/chat/browser/chatEditing/chatEditingModifiedDocumentEntry.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ChatEditingCodeEditorIntegration } from '../../../contrib/chat/browser/chatEditing/chatEditingCodeEditorIntegration.js';
import { URI } from '../../../../base/common/uri.js';
import { ModelDecorationOptions } from '../../../../editor/common/model/textModel.js';
import { MinimapPosition, OverviewRulerLane, IModelDeltaDecoration, ITextModel } from '../../../../editor/common/model.js';
import { themeColorFromId } from '../../../../base/common/themables.js';
import { RenderOptions, LineSource, renderLines } from '../../../../editor/browser/widget/diffEditor/components/diffEditorViewZones/renderLines.js';
import { overviewRulerAddedForeground, minimapGutterAddedBackground, } from '../../../../workbench/contrib/scm/common/quickDiff.js';
import { IKwaiPilotBridgeAPIService } from '../common/kwaiPilotBridgeAPIService.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { relativePath as getRelativePath } from '../../../../base/common/resources.js';

import './media/editCodeService.css';
import { IDocumentDiff } from '../../../../editor/common/diff/documentDiffProvider.js';
import { DetailedLineRangeMapping } from '../../../../editor/common/diff/rangeMapping.js';
import { WEBVIEW_BRIDGE_EVENT_NAME } from './kwaiPilotBridgeAPIService.js';
import { KwaipilotCommands } from './kwaipilotCommands.js';
import { CommandsRegistry, ICommandService } from '../../../../platform/commands/common/commands.js';
import { IContextKeyService, IContextKey, ContextKeyExpr } from '../../../../platform/contextkey/common/contextkey.js';
import { KeybindingsRegistry, KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';
import { KeyMod, KeyCode } from '../../../../base/common/keyCodes.js';

/**
 * Service for handling code editing and diff functionality in KwaiPilot.
 * Manages the lifecycle of document entries and editor integrations.
 */
export class EditCodeService extends Disposable implements IEditCodeService {
	_serviceBrand: undefined;

	private readonly _onDiffAccepted = this._register(new Emitter<DiffAcceptedEvent>());
	readonly onDiffAccepted = this._onDiffAccepted.event;

	private readonly _onDiffRejected = this._register(new Emitter<DiffRejectedEvent>());
	readonly onDiffRejected = this._onDiffRejected.event;

	private readonly _onDidAddOrDeleteDiffAreas = this._register(new Emitter<{ uri: URI }>());
	readonly onDidAddOrDeleteDiffAreas = this._onDidAddOrDeleteDiffAreas.event;

	private readonly _onDidChangeDiffsInArea = this._register(new Emitter<{ uri: URI; areaId: string }>());
	readonly onDidChangeDiffsInArea = this._onDidChangeDiffsInArea.event;

	private readonly _onDidChangeStreamingInArea = this._register(new Emitter<{ uri: URI; areaId: string }>());
	readonly onDidChangeStreamingInArea = this._onDidChangeStreamingInArea.event;

	private readonly documentEntries = new Map<string, ChatEditingModifiedDocumentEntry>();
	private readonly editorIntegrations = new Map<string, ChatEditingCodeEditorIntegration>();
	private readonly diffAreas = new Map<string, DiffArea>();
	private readonly diffs = new Map<string, any>(); // 兼容 Monaco diff
	private readonly streamingStates = new Map<string, {
		originalModel: ITextModel;
		modifiedModel: ITextModel;
		currentContent: string;
		initialContent: string; // 新增，保存最初的原始内容
		decorations: IEditorDecorationsCollection;
		viewZones: string[];
		disposables: DisposableStore;
	}>();
	private readonly diffDecorators = new Map<string, DisposableStore>();

	private _currentStreamingState: StreamingState | undefined;
	private hideFooterShortCut: boolean = false;

	private isStreaming: boolean = false; // 用于标记是否正在流式输出
	private isRestoring: boolean = false; // 用于标记是否正在恢复状态

	private diffFooterDom: HTMLDivElement | undefined;
	private currentDiffIndex: number = 0;

	private isProgrammaticScroll: boolean = false;
	// 记录每个文件下 chunk 的 accept 状态
	private readonly chunkAcceptStates = new Map<string, string[]>(); // uri ->chunkId[]

	// 记录每个文件下 chunk 的 reject 状态
	private readonly chunkRejectStates = new Map<string, string[]>(); // uri -> chunkId[]

	// 新增：浮动按钮容器
	private floatingChunkWidgets: {
		id: string;
		widget: IContentWidget;
		chunkId: string;
		diffEntry: DetailedLineRangeMapping;
		domNode: HTMLElement;
		viewZoneId?: string;
	}[] = [];
	private decorationCollection: {
		diffEntry: DetailedLineRangeMapping;
		decorationIds?: number[];
	}[] = [];

	// 滚动更新 footer 监听器
	private scrollUpdateFooterListener: DisposableStore | undefined;

	private readonly _diffModeContext: IContextKey<boolean>;

	constructor(
		@IEditorService private readonly editorService: IEditorService,
		@ITextFileService private readonly textFileService: ITextFileService,
		@IFileService private readonly fileService: IFileService,
		@IEditorWorkerService private readonly editorWorkerService: IEditorWorkerService,
		@IModelService private readonly modelService: IModelService,
		@ILogService private readonly logService: ILogService,
		@IKwaiPilotBridgeAPIService private readonly kwaiPilotBridgeAPIService: IKwaiPilotBridgeAPIService,
		@IWorkspaceContextService private readonly workspaceContextService: IWorkspaceContextService,
		@IContextKeyService contextKeyService: IContextKeyService,
		@ICommandService commandService: ICommandService,

	) {
		super();

		this._diffModeContext = contextKeyService.createKey('kwaipilot.diffMode', false);
		this._onDidAddOrDeleteDiffAreas.event(({ uri }) => {
			const diffAreas = this.getDiffAreas(uri);
			const newValue = diffAreas.length > 0;
			this._diffModeContext.set(newValue);
		});

		// 监听编辑器切换，确保浮层正确刷新/消失
		this.editorService.onDidActiveEditorChange(async () => {
			if (this.scrollUpdateFooterListener) {
				this.scrollUpdateFooterListener.dispose();
				this.scrollUpdateFooterListener = undefined;
			}
			const editor = this.editorService.activeTextEditorControl as ICodeEditor;
			const model = editor?.getModel();
			this.currentDiffIndex = 0;

			// 清理之前文件的流式状态，防止跨文件污染
			if (this._currentStreamingState && model) {
				const currentUri = model.uri.path.toString();
				const streamingUri = this._currentStreamingState.uri.path.toString();
				if (currentUri !== streamingUri) {
					// 清理流式状态，但保留 diff 状态
					this._currentStreamingState = undefined;
					this.isStreaming = false;
				}
			}

			if (model) {
				const uri = model.uri;
				try {
					// 所有 diff 文件 uri，如果所有代码已经提交，则清空 diff 状态
					const diffUris = this.streamingStates.keys();
					const allFilesChange: string[] = [];
					const currentFileChange: string[] = [];
					for (const diffUri of diffUris) {
						// 获取特定仓库的状态
						const repositoryState = await commandService.executeCommand('git.api.getRepositoryState', diffUri.toString());
						if (repositoryState) {
							allFilesChange.push(
								...repositoryState.workingTreeChanges,
								...repositoryState.indexChanges,
								...repositoryState.mergeChanges
							);
							if (diffUri.toString() === uri.path.toString()) {
								currentFileChange.push(
									...repositoryState.workingTreeChanges,
									...repositoryState.indexChanges,
									...repositoryState.mergeChanges
								);
							}
						}
					}
					if (allFilesChange.length === 0) {
						this.resetAllDiffs();
					} else if (currentFileChange.length === 0) {
						this.clearAllDiffState(uri);
					} else {
						this.updateDiffDecorations(uri);
						this.updateDiffFooter(uri);
					}

				} catch (error) {
					await this.updateDiffDecorations(uri);
					await this.updateDiffFooter(uri);
				}


			} else {
				this.removeDiffFooter();
				this.removeChunkButtons();
			}
		});

		this.fileService.onDidFilesChange(e => {
			if (this.isRestoring || this.isStreaming) {
				return;
			}
			const uris: string[] = [];
			if (e.rawUpdated) uris.push(...e.rawUpdated.map(u => u.toString()));
			if (e.rawAdded) uris.push(...e.rawAdded.map(u => u.toString()));
			if (e.rawDeleted) uris.push(...e.rawDeleted.map(u => u.toString()));
			for (const uri of uris) {
				const diffAreas = this.getDiffAreas(URI.parse(uri));
				if (diffAreas && diffAreas.length > 0) {
					// 使用 textFileService 重新读取文件内容
					this.textFileService.read(URI.parse(uri)).then(fileContent => {
						if (fileContent) {
							// 然后再刷新 diff
							this.refreshDiffOnFileChange(URI.parse(uri), fileContent.value);
						}
					});

				}
			}
		});

		this.kwaiPilotBridgeAPIService.bridge.addMessageListener(WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_STATE_UPDATE, (composerState: any) => {
			console.log('composerState', composerState);
		});


		this.initSidebarDiffCommands();
	}


	/** 注册 sidebar diff 命令，用于同步sidebar diff 状态到编辑器 */
	initSidebarDiffCommands() {
		CommandsRegistry.registerCommand({
			id: KwaipilotCommands.KeepDiff,
			handler: (accessor, params: { filepath?: string; abortChat?: boolean }) => {
				if (params.filepath) {
					this.acceptDiff({ uri: params.filepath ? this.getAbsoluteUri(params.filepath) : undefined });
				} else {
					this.acceptAllDiffs();
				}

			}
		});
		CommandsRegistry.registerCommand({
			id: KwaipilotCommands.UndoDiff,
			handler: (accessor, params: { filepath?: string; abortChat?: boolean }) => {
				if (params.filepath) {
					this.rejectDiff({ uri: params.filepath ? this.getAbsoluteUri(params.filepath) : undefined });
				} else {
					this.rejectAllDiffs();
				}
			}
		});
		CommandsRegistry.registerCommand({
			id: KwaipilotCommands.ClearAllDiffState,
			handler: (accessor) => {
				this.resetAllDiffs();
			}
		});
	}

	private getAbsoluteUri(relativePath: string): URI {
		// 获取工作区根目录
		const workspaceFolders = this.workspaceContextService.getWorkspace().folders;
		if (workspaceFolders.length === 0) {
			return URI.file(relativePath);
		}
		// 使用第一个工作区文件夹作为基准
		const workspaceRoot = workspaceFolders[0].uri;
		// 将相对路径转换为绝对路径
		return URI.joinPath(workspaceRoot, relativePath);
	}

	private refreshDiffOnFileChange(uri: URI, newContent: string) {
		const streamingState = this.streamingStates.get(uri.path.toString());
		if (!streamingState) {
			this.removeDiffFooter();
			this.removeChunkButtons();
			return;
		}

		if (newContent === streamingState.currentContent) {
			return;
		}
		// 更新当前流式状态
		this._currentStreamingState = {
			uri,
			content: newContent,
			initialContent: streamingState.initialContent,
			isFinal: true
		};

		this.streamingStates.set(uri.path.toString(), {
			...streamingState,
			currentContent: newContent,
		});

		this._updateDiffContent(this._currentStreamingState, false);
		this.updateDiffFooter(uri);
	}

	private async initStreamingState(params: {
		uri: URI;
		initialContent: string;
		currentContent: string;
	}): Promise<{
		originalModel: ITextModel;
		modifiedModel: ITextModel;
		currentContent: string;
		initialContent: string;
		decorations: IEditorDecorationsCollection;
		viewZones: string[];
		disposables: DisposableStore;
	} | undefined> {
		const { uri, initialContent, currentContent } = params;
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			return undefined;
		}

		// Create temporary models for diff
		const tempOriginalUri = uri.with({ scheme: 'kwaipilot-diff-original' });
		const tempModifiedUri = uri.with({ scheme: 'kwaipilot-diff-modified' });

		const existingOriginalModel = this.modelService.getModel(tempOriginalUri);
		if (existingOriginalModel) {
			this.modelService.destroyModel(tempOriginalUri);
		}

		const existingModifiedModel = this.modelService.getModel(tempModifiedUri);
		if (existingModifiedModel) {
			this.modelService.destroyModel(tempModifiedUri);
		}

		const mainModel = this.modelService.getModel(uri);
		const language = mainModel ? mainModel.getLanguageId() : null;

		const originalModel = this.modelService.createModel(
			initialContent,
			language as any,
			tempOriginalUri,
			true
		);

		const modifiedModel = this.modelService.createModel(
			currentContent,
			language as any,
			tempModifiedUri,
			true
		);

		const state = {
			originalModel,
			modifiedModel,
			currentContent,
			initialContent, // 新增
			decorations: editor.createDecorationsCollection(),
			viewZones: [],
			disposables: new DisposableStore()
		};

		return state;
	}

	// 1. 监听主 model 内容变更，实时刷新 diff
	private registerModelContentListener(model: ITextModel) {
		const uriStr = model.uri.path.toString();
		if (model.getValue() === this._currentStreamingState?.content) {
			return;
		}
		if (!this.diffDecorators.has(uriStr)) {
			const disposables = new DisposableStore();
			disposables.add(model.onDidChangeContent(() => {
				if (this.isRestoring || this.isStreaming) {
					return;
				}
				this.updateDiffDecorations(model.uri);
				this.updateDiffFooter(model.uri);
			}));
			this.diffDecorators.set(uriStr, disposables);
		}
	}

	/**
	 * Shows a diff of the proposed changes in the current editor.
	 * Creates a new document entry if one doesn't exist and sets up the editor integration.
	 * The diff is shown inline using decorations and viewzones.
	 *
	 * @param params Parameters containing the original and modified content
	 */
	async showDiff(params: ShowDiffParams): Promise<void> {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			return;
		}

		const model = editor.getModel();
		if (!model || model.uri.toString() !== params.uri.toString()) {
			return;
		}

		this.registerModelContentListener(model);

		// Store original content for potential revert
		const originalContent = model.getValue();


		this.logService.info(`[KwaiPilot] showDiff: uri=${params.uri.path.toString()}, originalContent=${originalContent}`);

		// Initialize streaming state if not exists
		let state = this.streamingStates.get(params.uri.path.toString());
		if (!state) {
			state = await this.initStreamingState({
				uri: params.uri,
				initialContent: originalContent,
				currentContent: params.modifiedContent || '',
			});
			if (!state) {
				return;
			}
			this.streamingStates.set(params.uri.path.toString(), state);
		}

		if (!params.modifiedContent) {
			return;
		}

		// 计算差异
		const diff = await this.editorWorkerService.computeDiff(
			state.originalModel.uri,
			state.modifiedModel.uri,
			{ computeMoves: false, ignoreTrimWhitespace: false, maxComputationTimeMs: Number.MAX_SAFE_INTEGER },
			'advanced'
		);

		this.logService.info(`[KwaiPilot] showDiff: diff=${JSON.stringify(diff)}`);

		if (!diff || diff.identical || !diff.changes || diff.changes.length === 0) {
			state.disposables.dispose();
			this.streamingStates.delete(params.uri.path.toString());
			return;
		}

		// Store the original content and diff information for potential revert
		this.genDiffArea(params.uri, diff, originalContent);

		// 渲染 view zone 和装饰器
		editor.changeViewZones(viewZoneChangeAccessor => {
			for (const id of state.viewZones) {
				viewZoneChangeAccessor.removeZone(id);
			}
			state.viewZones.length = 0;
		});
		this.renderDiffViewZones(diff, editor, state.originalModel, state.modifiedModel, state.viewZones, false);

		// 通知 diff 区域变更
		this._onDidAddOrDeleteDiffAreas.fire({ uri: params.uri });
	}

	private createDiffDecorations(diff: IDocumentDiff, editor: ICodeEditor) {
		// 清空之前的装饰器，避免叠加
		const decorationsCollection = editor.createDecorationsCollection();
		decorationsCollection.set([]); // 清空所有旧装饰器
		this.decorationCollection = []; // 清空之前的装饰器记录

		const decorations: IModelDeltaDecoration[] = [];

		const decorationIds: number[] = [];

		// 关键日志：确认 editor 当前 model 与 decorations 目标 model

		// 创建行级装饰器选项
		const diffLineAddDecorationBackground = ModelDecorationOptions.createDynamic({
			className: 'line-insert',
			description: 'kwaiPilot-line-insert',
			isWholeLine: true,
			marginClassName: 'gutter-insert',
			overviewRuler: {
				color: themeColorFromId(overviewRulerAddedForeground),
				position: OverviewRulerLane.Left
			},
			minimap: {
				color: themeColorFromId(minimapGutterAddedBackground),
				position: MinimapPosition.Gutter
			}
		});

		// const diffLineDeleteDecorationBackground = ModelDecorationOptions.createDynamic({
		// 	className: 'line-delete',
		// 	description: 'kwaipilot-line-delete',
		// 	isWholeLine: true,
		// 	marginClassName: 'gutter-delete',
		// 	overviewRuler: {
		// 		color: themeColorFromId(overviewRulerDeletedForeground),
		// 		position: OverviewRulerLane.Left
		// 	},
		// 	minimap: {
		// 		color: themeColorFromId(minimapGutterDeletedBackground),
		// 		position: MinimapPosition.Gutter
		// 	}
		// });

		// 为每个变更创建装饰器
		for (const [_, change] of diff.changes.entries()) {
			if ((change.original && !change.original.isEmpty) && (change.modified && change.modified.isEmpty)) {
				// for (let line = change.original.startLineNumber; line < change.original.endLineNumberExclusive; line++) {
				// 	decorationIds.push(decorations.push({
				// 		range: new Range(Math.max(line + addLineCount, lineCount), 1, Math.max(line + addLineCount, lineCount), 1),
				// 		options: diffLineDeleteDecorationBackground
				// 	}));
				// }
			} else if (change.modified && !change.modified.isEmpty && (change.original && change.original.isEmpty)) {
				// 只在纯新增时渲染新增装饰器
				for (let line = change.modified.startLineNumber; line < change.modified.endLineNumberExclusive; line++) {
					decorationIds.push(decorations.push({
						range: new Range(line, 1, line, 1),
						options: diffLineAddDecorationBackground
					}));
				}
			} else if ((change.original && !change.original.isEmpty) && (change.modified && !change.modified.isEmpty)) {
				// 修改（替换）类型：原始区间渲染删除，新区间渲染新增
				for (let line = change.modified.startLineNumber; line < change.modified.endLineNumberExclusive; line++) {
					decorationIds.push(decorations.push({
						range: new Range(line, 1, line, 1),
						options: diffLineAddDecorationBackground
					}));
				}
			}
			this.decorationCollection.push({
				diffEntry: change,
				decorationIds: decorationIds
			});
		}
		// 新增日志：装饰器分布统计
		const lineDecorMap: Record<number, number> = {};
		for (const decor of decorations) {
			const line = decor.range.startLineNumber;
			lineDecorMap[line] = (lineDecorMap[line] || 0) + 1;
		}
		return decorations;
	}

	/**
	 * Accepts the current diff changes.
	 * This will remove the diff decorations since the changes are already in the file.
	 *
	 * @param params Parameters for accepting the diff
	 */
	async acceptDiff(params: AcceptDiffParams): Promise<void> {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			return;
		}

		const model = editor.getModel();
		if (!model) {
			return;
		}

		if (!params.uri) {
			return;
		}

		this.updateExtensionStatusAfterFileOperate(params.uri, 'accept');

		// 获取对应的 diff 区域
		const diffArea = this.getDiffAreas(params.uri)[0];

		if (!diffArea) {
			return;
		}
		this.isRestoring = true; // 加锁

		this.clearAllDiffState(params.uri!);


		// 触发事件
		this._onDiffAccepted.fire({
			uri: params.uri!,
			changes: diffArea.diffs.map(diff => ({
				originalRange: diff.originalRange,
				modifiedRange: diff.modifiedRange,
				content: diff.content
			}))
		});
		this.isRestoring = false; // 解锁
	}

	/**
	 * Rejects the current diff changes.
	 * This will revert the file to its original state and clean up the diff decorations.
	 *
	 * @param params Parameters for rejecting the diff
	 */
	async rejectDiff(params: RejectDiffParams): Promise<void> {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			return;
		}

		const model = editor.getModel();
		if (!model) {
			return;
		}

		this.updateExtensionStatusAfterFileOperate(params.uri!, 'accept');

		// 获取对应的 diff 区域
		const diffArea = this.getDiffAreas(params.uri!)[0];

		if (!diffArea) {
			return;
		}

		// 恢复原始内容
		try {
			this.isRestoring = true; // 加锁
			// 先恢复主 model 内容
			model.setValue(diffArea.diffs[0].content);
			await this.textFileService.write(params.uri!, diffArea.diffs[0].content, {
				writeElevated: false,
			});
			await this.textFileService.save(params.uri!, {
				ignoreModifiedSince: true,
			});
		} catch (error) {
			this.isRestoring = false; // 解锁
			this.logService.error(`[KwaiPilot] rejectDiff: Failed to revert file: ${error}`);
			return;
		}

		this.clearAllDiffState(params.uri!);

		// 触发事件
		this._onDiffRejected.fire({
			uri: params.uri!,
			range: diffArea.diffs[0].modifiedRange
		});
		this.isRestoring = false; // 解锁
	}

	private async clearAllDiffState(uri: URI): Promise<void> {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			return;
		}

		const model = editor.getModel();
		if (!model) {
			return;
		}

		// 获取对应的 diff 区域
		const diffAreas = this.getDiffAreas(uri);

		// 清理主 model 的 diff 装饰器
		if (model) {
			const allDecorations = model.getAllDecorations();
			const decorationsToRemove = allDecorations.filter(d => {
				// 只删除我们创建的装饰器
				const description = d.options.description;
				return /kwaipilot/.test(description) || /TextEditorDecorationType/.test(d.options.className || '');
			});
			if (decorationsToRemove.length > 0) {
				model.deltaDecorations(decorationsToRemove.map(d => d.id), []);
			}
		}

		// 清理装饰器
		const disposables = this.diffDecorators.get(uri.path.toString());
		if (disposables) {
			disposables.dispose();
			this.diffDecorators.delete(uri.path.toString());
		}

		if (diffAreas && diffAreas.length > 0) {
			diffAreas.forEach(diffArea => {
				if (diffArea.uri.path === uri.path) {
					// 移除 diff 区域
					this.diffAreas.delete(diffArea.id);
					this.diffs.delete(diffArea.id);
				}
			});
		}

		const streamingState = this.streamingStates.get(uri.path.toString());

		// 清理 decorationsCollection
		if (streamingState?.decorations) {
			streamingState.decorations.set([]);
		}

		if (streamingState && streamingState.viewZones.length > 0) {
			editor.changeViewZones(viewZoneChangeAccessor => {
				for (const id of streamingState.viewZones) {
					viewZoneChangeAccessor.removeZone(id);
				}
				streamingState.viewZones.length = 0;
			});
		}
		this.streamingStates.delete(uri.path.toString());

		// 清理 footer和浮动按钮
		this.removeChunkButtons();
		this.handleChunkBtnAndFooterAfterAllChunksHandled(uri);
		this.clearChunkAcceptStates(uri);

		this.diffDecorators.delete(uri.path.toString());
		this.chunkAcceptStates.delete(uri.path.toString());
		this.chunkRejectStates.delete(uri.path.toString());
	}

	/**
	 * Gets all diff areas for a given URI.
	 *
	 * @param uri The URI to get diff areas for
	 */
	getDiffAreas(uri: URI): DiffArea[] {
		return Array.from(this.diffAreas.values()).filter(area => area.uri.path === uri.path);
	}

	/**
	 * Gets a diff by its ID.
	 *
	 * @param id The ID of the diff to get
	 */
	getDiff(id: string): Diff | undefined {
		return this.diffs.get(id);
	}
	/**
	 * Accepts all diffs in all files.
	 */
	async acceptAllDiffs(): Promise<void> {
		for (const diffArea of this.diffAreas.values()) {
			await this.acceptDiff({ uri: diffArea.uri });
		}
	}

	/**
	 * Accepts all diffs in all files.
	 */
	async resetAllDiffs(): Promise<void> {
		for (const diffArea of this.diffAreas.values()) {
			await this.clearAllDiffState(diffArea.uri);
		}
	}

	/**
	 * Rejects all diffs in all files.
	 */
	async rejectAllDiffs(): Promise<void> {
		for (const diffArea of this.diffAreas.values()) {
			await this.rejectDiff({ uri: diffArea.uri });
		}
	}

	/**
	 * Disposes of all document entries and editor integrations.
	 * Cleans up any remaining decorations and viewzones.
	 */
	override dispose(): void {
		for (const [_, entry] of this.documentEntries) {
			entry.dispose();
		}
		this.documentEntries.clear();

		for (const [_, integration] of this.editorIntegrations) {
			integration.dispose();
		}
		this.editorIntegrations.clear();

		this.diffAreas.clear();
		this.diffs.clear();

		for (const [_, disposables] of this.diffDecorators) {
			disposables.dispose();
		}
		this.diffDecorators.clear();

		super.dispose();
	}

	/**
	 * 更新 diff 内容
	 * @param uri 文件 URI
	 * @param content 更新内容
	 * @param isFinal 是否是最终更新
	 */
	async updateDiff(params: UpdateDiffParams): Promise<void> {
		const { uri, content, isFinal } = params;
		this.logService.info(`[KwaiPilot][Log] updateDiff: Start for content=${content}, isFinal=${isFinal}`);

		const editor = this.editorService.activeTextEditorControl as ICodeEditor;

		this.isStreaming = true;

		// 获取或初始化 streaming state
		let streamingState = this.streamingStates.get(uri.path.toString());

		if (editor) {
			this.setStreamingMask(editor, !isFinal);
		}
		// 获取或初始化 streaming state
		if (!streamingState) {
			const editor = this.editorService.activeTextEditorControl as ICodeEditor;
			if (!editor) {
				return;
			}
			const model = editor.getModel();
			if (!model) {
				return;
			}
			streamingState = await this.initStreamingState({
				uri,
				initialContent: model.getValue(),
				currentContent: content,
			});
			if (!streamingState) {
				return;
			}
			this.streamingStates.set(uri.path.toString(), streamingState);
		}

		// 检查当前活跃编辑器的 URI 是否与目标 URI 匹配
		const currentModelUri = editor?.getModel()?.uri;
		if (!currentModelUri || currentModelUri.path.toString() !== uri.path.toString()) {
			return;
		}

		// 更新当前流式状态
		this._currentStreamingState = {
			uri,
			content,
			initialContent: streamingState.initialContent,
			isFinal
		};

		// 直接更新 diff 内容
		await this._updateDiffContent(this._currentStreamingState);

		if (isFinal) {
			this.isStreaming = false;
			this.updateDiffFooter(uri);
		}
	}

	/** 获取 diffArea */
	private genDiffArea(uri: URI, diff: IDocumentDiff, initialContent: string): DiffArea {
		// 先清空当前 uri 对应的 diffArea
		const diffAreas = this.getDiffAreas(uri);
		const currentAreaId = diffAreas[0]?.id;

		const areaId = currentAreaId || `diff-area-${Date.now()}`;
		const diffArea: DiffArea = {
			id: areaId,
			uri: uri,
			startLine: diff.changes[0]?.modified.startLineNumber ?? 0,
			endLine: diff.changes[diff.changes.length - 1]?.modified.endLineNumberExclusive ?? 0,
			diffs: diff.changes.map(change => ({
				id: `${areaId}-${change.modified.startLineNumber}`,
				areaId,
				originalRange: new Range(
					change.original.startLineNumber,
					1,
					change.original.endLineNumberExclusive,
					1
				),
				modifiedRange: new Range(
					change.modified.startLineNumber,
					1,
					change.modified.endLineNumberExclusive,
					1
				),
				content: initialContent,
				change
			}))
		};

		// 存储 diff 区域
		this.diffAreas.set(areaId, diffArea);
		this.diffs.set(areaId, diff);

		return diffArea;
	}

	private async _updateDiffContent(state: StreamingState, writeFile: boolean = true) {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			return;
		}

		// 关键修复：检查当前活跃编辑器的 URI 是否与目标 URI 匹配
		const currentModelUri = editor.getModel()?.uri;
		if (!currentModelUri || currentModelUri.path.toString() !== state.uri.path.toString()) {
			return;
		}

		const streamingState = this.streamingStates.get(state.uri.path.toString());
		if (!streamingState) {
			return;
		}

		// 流式 diff：原始内容不截断，避免补空行，保证 diff 算法能正确识别新增/替换行
		const newLines = state.content.split('\n');
		const originalLines = state.initialContent.split('\n');
		const truncatedOriginal = originalLines.join('\n');
		const truncatedNew = newLines.join('\n');
		streamingState.currentContent = state.content;
		streamingState.modifiedModel.setValue(truncatedNew);
		streamingState.originalModel.setValue(truncatedOriginal);


		if (state.isFinal) {
			if (writeFile) {
				// 再次确认 URI 匹配（双重保险）
				const finalModelUri = editor.getModel()?.uri;
				if (!finalModelUri || finalModelUri.path.toString() !== state.uri.path.toString()) {
					return;
				}

				try {
					await this.textFileService.write(state.uri, state.content, {
						writeElevated: false
					});
				} catch (error) {
					this.logService.error(`[KwaiPilot] updateDiff: Failed to write file: ${error}`);
					return;
				}

			}

			streamingState.originalModel.setValue(state.initialContent);
			streamingState.modifiedModel.setValue(state.content);
			const diff = await this.editorWorkerService.computeDiff(
				streamingState.originalModel.uri,
				streamingState.modifiedModel.uri,
				{ computeMoves: false, ignoreTrimWhitespace: false, maxComputationTimeMs: Number.MAX_SAFE_INTEGER },
				'advanced'
			);

			if (!diff || diff.identical || !diff.changes || diff.changes.length === 0) {
				// 清空当前文件的 diffAreas
				this.clearAllDiffState(state.uri);
				return;
			}

			const decorationsData = this.createDiffDecorations(diff, editor);
			streamingState.decorations.set(decorationsData);

			this.genDiffArea(state.uri, diff, state.initialContent);
			// 通知 diff 区域变更
			this._onDidAddOrDeleteDiffAreas.fire({ uri: state.uri });

			// 只清理 view zone，不删除 streamingState
			editor.changeViewZones((viewZoneChangeAccessor: { removeZone: (id: string) => void; addZone: (zone: IViewZone) => string }) => {
				for (const id of streamingState.viewZones) {
					viewZoneChangeAccessor.removeZone(id);
				}
				streamingState.viewZones.length = 0;
			});
			this.renderDiffViewZones(diff, editor, streamingState.originalModel, streamingState.modifiedModel, streamingState.viewZones, state.isFinal);
		} else {
			// 流式 diff（只到新代码最后一行）
			const diff = await this.editorWorkerService.computeDiff(
				streamingState.originalModel.uri,
				streamingState.modifiedModel.uri,
				{ computeMoves: false, ignoreTrimWhitespace: false, maxComputationTimeMs: Number.MAX_SAFE_INTEGER },
				'advanced'
			);
			if (!diff || diff.identical || !diff.changes || diff.changes.length === 0) {
				return;
			}
			const decorationsData = this.createDiffDecorations(diff, editor);
			streamingState.decorations.set(decorationsData);
			editor.changeViewZones((viewZoneChangeAccessor: { removeZone: (id: string) => void; addZone: (zone: IViewZone) => string }) => {
				for (const id of streamingState.viewZones) {
					viewZoneChangeAccessor.removeZone(id);
				}
				streamingState.viewZones.length = 0;
			});
			this.renderDiffViewZones(diff, editor, streamingState.originalModel, streamingState.modifiedModel, streamingState.viewZones, state.isFinal);
		}
	}

	/**
	 * 渲染 diff 的 view zone（支持流式和一次性 diff）
	 */
	private renderDiffViewZones(
		diff: IDocumentDiff,
		editor: ICodeEditor,
		originalModel: ITextModel,
		modifiedModel: ITextModel,
		viewZones: string[],
		isFinal: boolean = false
	) {
		const model = editor.getModel();
		if (isFinal) {
			this.removeChunkButtons();
			this.floatingChunkWidgets = [];
		}

		editor.changeViewZones(viewZoneChangeAccessor => {
			for (const id of viewZones) {
				viewZoneChangeAccessor.removeZone(id);
			}
			viewZones.length = 0;
			const mightContainNonBasicASCII = originalModel.mightContainNonBasicASCII();
			const mightContainRTL = originalModel.mightContainRTL();
			const renderOptions = RenderOptions.fromEditor(editor);
			const sortedChanges = [...diff.changes].sort((a, b) =>
				a.modified.startLineNumber - b.modified.startLineNumber
			);
			for (const [idx, diffEntry] of sortedChanges.entries()) {
				try {
					let domNode: HTMLDivElement | undefined = undefined;
					let afterLine: number;
					let viewZoneId: string | undefined = undefined;
					const isInsert = diffEntry.modified && !diffEntry.modified.isEmpty && diffEntry.original && diffEntry.original.isEmpty;
					const chunkId = `diff-chunk-${idx}`;
					if (!diffEntry.original.isEmpty) {
						const source = new LineSource(
							diffEntry.original.mapToLineArray((l: any) => originalModel.tokenization.getLineTokens(l)),
							[],
							mightContainNonBasicASCII,
							mightContainRTL,
						);

						const domNode = document.createElement('div');
						domNode.classList.add('chat-editing-original-zone', 'view-lines', 'line-delete', 'monaco-mouse-cursor-text');
						const result = renderLines(source, renderOptions, [], domNode);
						afterLine = Math.max(0, diffEntry.modified.startLineNumber - 1);

						const viewZoneData: IViewZone = {
							afterLineNumber: afterLine,
							heightInLines: result.heightInLines || 1,
							domNode,
							ordinal: 50000 + idx * 2
						};
						// 在第几行后添加空行，添加几行
						viewZoneId = viewZoneChangeAccessor.addZone(viewZoneData);
						viewZones.push(viewZoneId);
					} else if (isInsert) {
						domNode = document.createElement('div');
						domNode.classList.add('kwaipilot-diff-insert-zone');
						const lines = [];
						for (let l = diffEntry.modified.startLineNumber; l < diffEntry.modified.endLineNumberExclusive; l++) {
							lines.push(modifiedModel.getLineContent(l));
						}
						const pre = document.createElement('pre');
						pre.textContent = lines.join('\n');
						pre.classList.add('kwaipilot-diff-insert-lines');
						domNode.appendChild(pre);
					}
					if (isFinal) {
						// contentWidget 按钮
						const chunkBtns = this.renderChunkButtons(
							idx,
							editor,
							() => {
								if (!model) return;
								this.setChunkAccepted(model.uri, chunkId, true);
							},
							() => {
								if (!model) return;
								this.setChunkRejected(model.uri, chunkId, false);
							}
						);
						chunkBtns.classList.add('kwaipilot-diff-chunk-btns-floating');
						const widgetId = `kwaipilot-diff-chunk-widget-${idx}`;
						/**
						 * 注意：超过了当前 model 的最大行数，Monaco 会自动将 widget 定位到最后一行的下方
						 * （即 maxLineNumber + 1 其实就是最后一行的下方），但视觉上会"吸附"在最后一行。
						 */
						const widget: IContentWidget = {
							getId: () => widgetId,
							getDomNode: () => chunkBtns,
							getPosition: () => ({
								position: { lineNumber: diffEntry.modified.endLineNumberExclusive, column: 1 },
								preference: [ContentWidgetPositionPreference.EXACT]
							}),
							allowEditorOverflow: true
						};
						editor.addContentWidget(widget);
						this.floatingChunkWidgets.push({
							id: widgetId,
							widget,
							chunkId,
							diffEntry,
							domNode: chunkBtns,
							viewZoneId
						});
						domNode?.appendChild(document.createComment('floating chunk btns moved to content widget'));
					}

				} catch (e) {
					this.logService.error(`[KwaiPilot][Log] renderDiffViewZones: [${idx}] Error rendering view zone: ${e}`);
				}
			}
		});
	}

	/** 获取代码块 accept / reject 按钮距离右侧的距离 */
	private getRightOffset(editor: ICodeEditor): number {
		const editorDom = editor.getDomNode();
		if (!editorDom) {
			return 20; // 默认偏移量
		}
		// 获取 minimap
		const minimap = editorDom.querySelector('.minimap');
		let minimapWidth = 0;
		if (minimap) {
			const rect = minimap.getBoundingClientRect();
			minimapWidth = rect.width || 0;
		}
		const scrollbar = editorDom.querySelector('.scrollbar.vertical .slider');
		let scrollbarWidth = 0;
		if (scrollbar) {
			const rect = scrollbar.getBoundingClientRect();
			scrollbarWidth = rect.width || 0;
		}
		// 计算右侧偏移量
		const rightOffset = (minimapWidth + scrollbarWidth) > 0 ? (minimapWidth + scrollbarWidth + 20) : 20;

		return rightOffset;
	}

	/**
	 * 渲染 diff chunk 的 accept/reject 按钮
	 */
	private renderChunkButtons(idx: number, editor: ICodeEditor, onAccept: () => void, onReject: () => void): HTMLDivElement {
		const chunkBtnContainer = document.createElement('div');
		chunkBtnContainer.classList.add('kwaipilot-diff-chunk-btns');
		const rightOffset = this.getRightOffset(editor);
		chunkBtnContainer.style.right = `${rightOffset}px`;
		const acceptBtn = document.createElement('button');
		// label
		const acceptLabel = document.createElement('span');
		acceptLabel.textContent = 'Accept';
		acceptBtn.appendChild(acceptLabel);
		// icon
		const acceptIcon = document.createElement('span');
		acceptIcon.classList.add('shortcut-text'); // VS Code accept icon
		// allow-any-unicode-next-line
		acceptIcon.textContent = '⌘Y';
		acceptBtn.appendChild(acceptIcon);
		acceptBtn.classList.add('kwaipilot-diff-chunk-accept-btn');

		const rejectBtn = document.createElement('button');
		// label
		const rejectLabel = document.createElement('span');
		rejectLabel.textContent = 'Reject';
		rejectBtn.appendChild(rejectLabel);
		// icon
		const rejectIcon = document.createElement('span');
		rejectIcon.classList.add('shortcut-text'); // VS Code reject icon
		// allow-any-unicode-next-line
		rejectIcon.textContent = '⌘N';
		rejectBtn.appendChild(rejectIcon);
		rejectBtn.classList.add('kwaipilot-diff-chunk-reject-btn');
		acceptBtn.onclick = onAccept;
		rejectBtn.onclick = onReject;
		chunkBtnContainer.appendChild(rejectBtn);
		chunkBtnContainer.appendChild(acceptBtn);

		return chunkBtnContainer;
	}

	/** 文件来回切换&文件内容没有改变时，更新 */
	private async updateDiffDecorations(uri: URI) {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.removeDiffFooter();
			this.removeChunkButtons();
			return;
		}
		if (this.isStreaming || this.isRestoring) {
			return;
		}

		try {
			const diffAreas = this.getDiffAreas(uri);
			if (!diffAreas || diffAreas.length === 0) {
				this.handleChunkBtnAndFooterAfterAllChunksHandled(uri);
				return;
			}
			const diffArea = diffAreas[0];
			const model = editor.getModel();
			if (!model) return;

			if (!model || model.uri.toString() !== uri.toString()) {
				return;
			}

			let streamingState = this.streamingStates.get(uri.path.toString());
			const initialContent = streamingState?.initialContent ?? diffArea.diffs[0]?.content ?? '';
			const modelContent = model.getValue();

			// 更新当前流式状态
			this._currentStreamingState = {
				uri,
				content: modelContent,
				initialContent,
				isFinal: true
			};

			// 获取或初始化 streaming state
			if (!streamingState) {
				streamingState = await this.initStreamingState({
					uri,
					initialContent,
					currentContent: modelContent,
				});
				if (!streamingState) {
					return;
				}
				this.streamingStates.set(uri.path.toString(), streamingState);
			}


			this.isRestoring = true; // <--- 关键：异步流程开始时加锁，防止多次调用导致状态混乱
			// 直接更新 diff 内容
			await this._updateDiffContent(this._currentStreamingState, false);
		} finally {
			this.isRestoring = false; // <--- 关键：异步流程结束后再解锁
		}
	}

	/** 更新 diff footer */
	private async updateDiffFooter(uri: URI) {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.removeDiffFooter();
			this.removeChunkButtons();
			return;
		}
		if (this.isStreaming) {
			return;
		}
		const diffAreas = this.getDiffAreas(uri);
		if (!diffAreas || diffAreas.length === 0) {
			this.handleChunkBtnAndFooterAfterAllChunksHandled(uri);
			return;
		}
		this.renderDiffFooter(editor, uri, diffAreas);
	}

	/** 没有代码块 diff 时，渲染  Review next file，跳转到下一个打开的文件 */
	private renderReviewNextFileFooter(editor: ICodeEditor, uri: URI, allDiffAreas: DiffArea[]) {
		this.removeDiffFooter();
		const container = editor.getContainerDomNode();
		const footer = document.createElement('div');
		footer.classList.add('kwaipilot-review-next-file-footer');

		const reviewBtn = document.createElement('button');
		reviewBtn.classList.add('review-next-file-btn');
		const label = document.createElement('span');
		label.textContent = 'Review next file';
		reviewBtn.appendChild(label);
		const icon = document.createElement('span');
		icon.classList.add('codicon', 'codicon-drop-down-button');
		reviewBtn.appendChild(icon);
		reviewBtn.onclick = () => {
			const nextUri = allDiffAreas[0].uri;
			if (nextUri) {
				this.editorService.openEditor({ resource: nextUri });
			}
		};
		footer.appendChild(reviewBtn);
		container.appendChild(footer);
		this.diffFooterDom = footer;
		this.currentDiffIndex = 0;
	}

	/** 当前文件所有代码块操作后，判断是否还有其他文件的 diff */
	private handleChunkBtnAndFooterAfterAllChunksHandled(uri: URI) {
		const allDiffAreas = Array.from(this.diffAreas.values());
		if (allDiffAreas.length > 0) {
			this.renderReviewNextFileFooter(this.editorService.activeTextEditorControl as ICodeEditor, uri, allDiffAreas);
			this.removeChunkButtons();
		} else {
			this.removeDiffFooter();
			this.removeChunkButtons();
		}
	}


	private removeChunkButtons() {
		// 清空 diff chunks
		this.floatingChunkWidgets.forEach(({ widget }) => {
			const editor = this.editorService.activeTextEditorControl as ICodeEditor;
			if (editor) {
				editor.removeContentWidget(widget);
			}
		});
		this.floatingChunkWidgets = [];
	}


	/** 快捷键 accept 代码块 */
	acceptCurrentChunk(editor: ICodeEditor, uri: URI) {
		// 获取当前 diff 区块
		const chunk = this.floatingChunkWidgets[this.currentDiffIndex];
		if (!chunk) {
			return;
		}
		// 执行 accept 操作
		this.setChunkAccepted(uri, chunk.chunkId, true).then(() => {
			// 操作后自动切换到下一个 diff 区块
			if (this.floatingChunkWidgets.length > 0) {
				// 保证 currentDiffIndex 不越界
				this.currentDiffIndex = Math.min(this.currentDiffIndex, this.floatingChunkWidgets.length - 1);
				this.scrollToDiff(editor, this.getDiffAreas(uri), this.currentDiffIndex);
				const footer = this.diffFooterDom;
				if (footer) {
					this.updateFooterIndex(footer, this.getDiffAreas(uri));
				}
			} else {
				this.handleChunkBtnAndFooterAfterAllChunksHandled(uri);
			}
		});
	}

	/** 快捷键 reject 代码块 */
	rejectCurrentChunk(editor: ICodeEditor, uri: URI) {
		// 获取当前 diff 区块
		const chunk = this.floatingChunkWidgets[this.currentDiffIndex];
		if (!chunk) {
			return;
		}
		// 执行 reject 操作
		this.setChunkRejected(uri, chunk.chunkId, true).then(() => {
			// 操作后自动切换到下一个 diff 区块
			if (this.floatingChunkWidgets.length > 0) {
				// 保证 currentDiffIndex 不越界
				this.currentDiffIndex = Math.min(this.currentDiffIndex, this.floatingChunkWidgets.length - 1);
				this.scrollToDiff(editor, this.getDiffAreas(uri), this.currentDiffIndex);
				const footer = this.diffFooterDom;
				if (footer) {
					this.updateFooterIndex(footer, this.getDiffAreas(uri));
				}
			} else {
				this.handleChunkBtnAndFooterAfterAllChunksHandled(uri);
			}
		});
	}

	/** 减少 footer 的total */
	private updateDiffFooterAfterChunkAcceptOrReject(uri: URI) {
		const footer = this.diffFooterDom;
		if (!footer) return;
		const diffAreas = this.getDiffAreas(uri);
		if (!diffAreas || diffAreas.length === 0) return;
		const indexSpan = footer.querySelector('.diff-index-label');
		const total = this.floatingChunkWidgets.length;
		if (this.currentDiffIndex >= total) {
			this.currentDiffIndex = total - 1;
		}
		if (indexSpan) {
			indexSpan.textContent = `${this.currentDiffIndex + 1} / ${total}`;
		}
	}


	/** 修改 renderDiffFooter，挂载/卸载文件级快捷键 */
	private renderDiffFooter(editor: ICodeEditor, uri: URI, diffAreas: DiffArea[]) {
		this.removeDiffFooter();

		// 添加滚动监听，更新当前的 currentDiffIndex
		if (this.scrollUpdateFooterListener) {
			this.scrollUpdateFooterListener.dispose();
		}
		this.scrollUpdateFooterListener = new DisposableStore();
		this.scrollUpdateFooterListener.add(editor.onDidScrollChange(() => {
			if (this.isProgrammaticScroll || this.isRestoring || this.isStreaming) return;
			const visibleRanges = editor.getVisibleRanges();
			if (visibleRanges.length === 0) return;

			const visibleCenter = (visibleRanges[0].startLineNumber + visibleRanges[0].endLineNumber) / 2;
			let closestDiffIndex = 0;
			let minDistance = Infinity;

			this.floatingChunkWidgets.forEach((widget, index) => {
				const widgetLine = widget.diffEntry.modified.startLineNumber;
				const distance = Math.abs(widgetLine - visibleCenter);
				if (distance < minDistance) {
					minDistance = distance;
					closestDiffIndex = index;
				}
			});

			if (this.currentDiffIndex !== closestDiffIndex) {
				this.currentDiffIndex = closestDiffIndex;
				const footer = this.diffFooterDom;
				if (footer) {
					this.updateFooterIndex(footer, this.getDiffAreas(uri));
				}
			}
		}));
		this.scrollUpdateFooterListener.add(editor.onDidLayoutChange(() => {
			const editorWidth = editor.getDomNode()?.clientWidth || 0;
			const hideFooterShortCut = editorWidth < 600;
			if (hideFooterShortCut !== this.hideFooterShortCut) {
				this.hideFooterShortCut = hideFooterShortCut;
				if (hideFooterShortCut) {
					this.diffFooterDom?.classList.add('kwaipilot-diff-footer-mini');
				} else {
					this.diffFooterDom?.classList.remove('kwaipilot-diff-footer-mini');
				}
			}
		}));

		const container = editor.getContainerDomNode();

		const footer = document.createElement('div');
		footer.classList.add('kwaipilot-diff-footer');
		footer.classList.toggle('kwaipilot-diff-footer-mini', this.hideFooterShortCut);

		const prevAndNextContainer = document.createElement('div');
		prevAndNextContainer.classList.add('prev-next-container');

		const prevBtn = document.createElement('button');
		prevBtn.classList.add('prev-diff-btn');
		const prevBtnIcon = document.createElement('span');
		prevBtnIcon.classList.add('codicon', 'codicon-drop-down-button');
		prevBtnIcon.onclick = () => {
			// 可以循环
			const prevIndex = this.currentDiffIndex - 1;
			this.currentDiffIndex = prevIndex < 0 ? diffAreas[0].diffs.length - 1 : prevIndex;
			this.scrollToDiff(editor, diffAreas, this.currentDiffIndex);
			this.updateFooterIndex(footer, diffAreas);
		};
		prevBtn.appendChild(prevBtnIcon);
		prevAndNextContainer.appendChild(prevBtn);

		// diff 索引
		const indexSpan = document.createElement('span');
		indexSpan.classList.add('diff-index-label');
		const total = diffAreas[0]?.diffs.length || 0;
		indexSpan.textContent = `${this.currentDiffIndex + 1} of ${total} `;
		prevAndNextContainer.appendChild(indexSpan);

		const nextBtn = document.createElement('button');
		nextBtn.classList.add('next-diff-btn');
		const nextBtnIcon = document.createElement('span');
		nextBtnIcon.classList.add('codicon', 'codicon-drop-down-button');
		nextBtnIcon.onclick = () => {
			const total = diffAreas[0].diffs.length;
			// 可以循环
			const nextIndex = this.currentDiffIndex + 1;
			this.currentDiffIndex = nextIndex >= total ? 0 : nextIndex;
			this.scrollToDiff(editor, diffAreas, this.currentDiffIndex);
			this.updateFooterIndex(footer, diffAreas);
		};
		nextBtn.appendChild(nextBtnIcon);
		prevAndNextContainer.appendChild(nextBtn);

		footer.appendChild(prevAndNextContainer);

		// Reject
		const rejectBtn = document.createElement('button');
		rejectBtn.classList.add('reject-file-btn');
		// label
		const rejectLabel = document.createElement('span');
		rejectLabel.textContent = 'Reject file';
		rejectBtn.appendChild(rejectLabel);
		rejectBtn.onclick = () => {
			this.rejectDiff({ uri });
		};
		// icon
		const rejectIcon = document.createElement('span');
		rejectIcon.classList.add('shortcut-text'); // VS Code discard icon
		// allow-any-unicode-next-line
		rejectIcon.textContent = '⇧⌘⌫';
		rejectBtn.appendChild(rejectIcon);

		footer.appendChild(rejectBtn);

		// Accept
		const acceptBtn = document.createElement('button');
		acceptBtn.classList.add('accept-file-btn');
		// label
		const acceptLabel = document.createElement('span');
		acceptLabel.textContent = 'Accept file';
		acceptBtn.appendChild(acceptLabel);
		acceptBtn.onclick = () => {
			this.acceptDiff({ uri });

		};
		// icon
		const acceptIcon = document.createElement('span');
		acceptIcon.classList.add('shortcut-text'); // VS Code accept icon
		// allow-any-unicode-next-line
		acceptIcon.textContent = '⌘⏎';
		acceptBtn.appendChild(acceptIcon);
		footer.appendChild(acceptBtn);

		const uris = Array.from(new Set(Array.from(this.diffAreas.values()).map(area => area.uri.toString())));
		const isMultipleDiffFile = uris.length > 1;

		if (isMultipleDiffFile) {
			const prevFileBtn = document.createElement('button');
			prevFileBtn.classList.add('prev-file-btn');
			const prevFileIcon = document.createElement('span');
			prevFileIcon.classList.add('codicon', 'codicon-drop-down-button');
			prevFileBtn.appendChild(prevFileIcon);
			// allow-any-unicode-next-line
			prevFileBtn.title = '上一个 diff 文件';
			prevFileBtn.onclick = () => {
				this.navigateDiffFile(-1);
			};
			footer.appendChild(prevFileBtn);

			const nextFileBtn = document.createElement('button');
			nextFileBtn.classList.add('next-file-btn');
			const nextFileIcon = document.createElement('span');
			nextFileIcon.classList.add('codicon', 'codicon-drop-down-button');
			nextFileBtn.appendChild(nextFileIcon);
			// allow-any-unicode-next-line
			nextFileBtn.title = '下一个 diff 文件';
			nextFileBtn.onclick = () => {
				this.navigateDiffFile(1);
			};
			footer.appendChild(nextFileBtn);
		}

		// 插入到编辑器容器
		container.appendChild(footer);
		this.diffFooterDom = footer;
		this.currentDiffIndex = 0;
		this.updateFooterIndex(footer, diffAreas);

		// 初始滚动到第一个 diff
		this.scrollToDiff(editor, diffAreas, 0);
	}

	/** 导航到下一个/上一个 diff 文件 */
	private navigateDiffFile(direction: number) {
		// direction: -1 = prev, 1 = next
		const uris = Array.from(new Set(Array.from(this.diffAreas.values()).map(area => area.uri.toString())));
		if (!uris.length) return;
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		const model = editor?.getModel();
		if (!model) return;

		// 精准匹配：比较完整的文件标识（scheme + authority + path + query + fragment）
		const currentUri = model.uri;
		let targetFileUri: URI | undefined;

		// 如果当前是 kwaipilot-diff-modified scheme，需要找到对应的原始文件
		if (currentUri.scheme === 'kwaipilot-diff-modified') {
			// 构造对应的 file:// URI 进行匹配
			targetFileUri = URI.file(currentUri.path);
		} else {
			targetFileUri = currentUri;
		}

		// 在 uris 中查找完全匹配的文件
		const curIdx = uris.findIndex(uri => {
			const parsedUri = URI.parse(uri);
			return parsedUri.toString() === targetFileUri!.toString();
		});

		if (curIdx === -1) return;
		let nextIdx = curIdx + direction;
		if (nextIdx < 0) nextIdx = uris.length - 1;
		if (nextIdx >= uris.length) nextIdx = 0;
		const nextUri = uris[nextIdx];
		// 打开下一个/上一个 diff 文件
		this.editorService.openEditor({ resource: URI.parse(nextUri) });
	}

	/** update footer 中目前的代码块索引 */
	private updateFooterIndex(footer: HTMLDivElement, diffAreas: DiffArea[]) {
		const indexSpan = footer.querySelector('.diff-index-label');
		if (indexSpan && diffAreas.length > 0) {
			const total = diffAreas[0].diffs.length;
			indexSpan.textContent = `${this.currentDiffIndex + 1} / ${total}`;
		}
	}

	private isSameNumber(num1: any, num2: any) {
		if (!num1 && !num2) return true;
		return num1 === num2;
	}

	private isSameDiffEntry(diffEntry1: DetailedLineRangeMapping, diffEntry2: DetailedLineRangeMapping): boolean {
		return this.isSameNumber(diffEntry1.modified.startLineNumber, diffEntry2.modified.startLineNumber) &&
			this.isSameNumber(diffEntry1.modified.endLineNumberExclusive, diffEntry2.modified.endLineNumberExclusive) &&
			this.isSameNumber(diffEntry1.original.startLineNumber, diffEntry2.original.startLineNumber) &&
			this.isSameNumber(diffEntry1.original.endLineNumberExclusive, diffEntry2.original.endLineNumberExclusive);

	}

	/** 滚动到指定 diff 行 */
	private scrollToDiff(editor: ICodeEditor, diffAreas: DiffArea[], idx: number) {
		if (!diffAreas.length) return;
		const diffs = diffAreas[0].diffs;
		if (!diffs || diffs.length === 0) return;
		const target = diffs[Math.max(0, Math.min(idx, diffs.length - 1))];
		this.isProgrammaticScroll = true;
		editor.revealLineInCenter(target.modifiedRange.startLineNumber);
		setTimeout(() => { this.isProgrammaticScroll = false; }, 0); // 微任务后解锁
	}

	/** 代码块 accept 之后，将原始代码的对应行改为修改后的代码，这样保证 diff 算法能正确识别 */
	private updateInitialContentAfterChunkAccept(uri: URI, chunkId: string) {
		const streamingState = this.streamingStates.get(uri.path.toString());
		if (!streamingState) return;
		const model = this.modelService.getModel(uri);
		if (!model) return;

		// 1. 先用 initialContent 作为基准
		const lines = streamingState.initialContent.split('\n');

		const chunkInfo = this.floatingChunkWidgets.find(widget => widget.chunkId === chunkId);
		const diffEntry = chunkInfo?.diffEntry;
		if (!chunkInfo || !diffEntry) {
			return;
		}
		const range = diffEntry.original; // {startLineNumber, endLineNumber}
		const modifiedRange = diffEntry.modified;
		if (!range || !modifiedRange) {
			return;
		}
		// 3. 用当前 model 的内容替换 initialContent 对应 chunk 区域
		const newLines = model.getValue().split('\n').slice(
			modifiedRange.startLineNumber - 1,
			modifiedRange.endLineNumberExclusive - 1
		);

		lines.splice(
			range.startLineNumber - 1,
			range.endLineNumberExclusive - range.startLineNumber,
			...newLines
		);

		// 4. 更新 initialContent
		streamingState.initialContent = lines.join('\n');
		if (this._currentStreamingState) {
			this._currentStreamingState = {
				...this._currentStreamingState,
				initialContent: streamingState.initialContent,
			};
		}
		// 更新 streamingStates
		this.streamingStates.set(uri.path.toString(), streamingState);
		// 更新 diffAreas 里面的  content
		const diffAreas = this.getDiffAreas(uri);
		if (diffAreas.length && diffAreas[0].diffs.length) {
			diffAreas[0].diffs[0].content = streamingState.initialContent;
		}
	}

	/** 设置 chunk 接受状态 */
	private async setChunkAccepted(uri: URI, chunkId: string, accepted: boolean) {

		const acceptChunkIds = this.chunkAcceptStates.get(uri.path.toString()) || [];
		acceptChunkIds.push(chunkId);
		this.chunkAcceptStates.set(uri.path.toString(), acceptChunkIds);

		// 更新 initialContent
		this.updateInitialContentAfterChunkAccept(uri, chunkId);

		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		this.removeChunkDecoration(uri, editor, chunkId);


		if (this.isAllChunksHandled(uri)) {
			this.clearAllDiffState(uri);
		} else {
			await this.updateDiffDecorations(uri);
			await this.updateDiffFooterAfterChunkAcceptOrReject(uri);
		}
	}

	/** 设置 chunk 拒绝状态 */
	private async setChunkRejected(uri: URI, chunkId: string, rejected: boolean) {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			return;
		}
		try {
			// 1. 设置 isRestoring 标记，防止文件变更事件触发不必要的更新
			this.isRestoring = true;
			const rejectChunkIds = this.chunkRejectStates.get(uri.path.toString()) || [];
			rejectChunkIds.push(chunkId);
			this.chunkRejectStates.set(uri.path.toString(), rejectChunkIds);
			// 2. 恢复原始代码
			await this.restoreChunkOriginalCode(uri, editor, chunkId);
			// 2. 只移除当前 chunk 的装饰器和 viewZone
			this.removeChunkDecoration(uri, editor, chunkId);
			// 3. 清理 UI 状态
			if (this.isAllChunksHandled(uri)) {
				await this.clearAllDiffState(uri);
			} else {
				await this.updateDiffDecorations(uri);
				await this.updateDiffFooter(uri);
			}
		} finally {
			// 4. 确保标记被清除
			this.isRestoring = false;
		}
	}

	/**
	 * 恢复当前 chunk 的原始代码，代码块拒绝后，将新代码的对应行恢复为原始代码，保证后面 diff 算法能正确识别
	 * @param editor 当前编辑器
	 * @param chunkId chunk 的唯一标识
	 * @returns 新代码
	 */
	private async restoreChunkOriginalCode(uri: URI, editor: ICodeEditor, chunkId: string): Promise<string | undefined> {
		const model = editor.getModel();
		if (!model) {
			return;
		}
		const streamingState = this.streamingStates.get(model.uri.path.toString());
		if (!streamingState) {
			return;
		}
		const chunkInfo = this.floatingChunkWidgets.find(widget => widget.chunkId === chunkId);
		const diffEntry = chunkInfo?.diffEntry;
		if (!chunkInfo || !diffEntry) {
			return;
		}

		// 1. 获取当前文件内容
		const currentLines = model.getValue().split('\n');

		// 2. 获取原始内容
		const originalRange = diffEntry.original;
		const originalLines = streamingState.initialContent.split('\n').slice(
			originalRange.startLineNumber - 1,
			originalRange.endLineNumberExclusive - 1
		);

		// 3. 获取修改范围
		const modifiedRange = diffEntry.modified;

		// 4. 替换内容
		currentLines.splice(
			modifiedRange.startLineNumber - 1,
			modifiedRange.endLineNumberExclusive - modifiedRange.startLineNumber,
			...originalLines
		);

		const newContent = currentLines.join('\n');

		if (this._currentStreamingState) {
			this._currentStreamingState = {
				...this._currentStreamingState,
				content: newContent,
			};
			// 更新 streamingStates
			this.streamingStates.set(uri.path.toString(), {
				...streamingState,
				currentContent: newContent,
			});
		}

		// 5. 直接写入文件，不再设置 isRestoring（因为在外层已经设置）
		try {
			await this.textFileService.write(model.uri, newContent, {
				writeElevated: false,
			});
			await this.textFileService.save(model.uri!, {
				ignoreModifiedSince: true,
			});
			return newContent;
		} catch (error) {
			this.logService.error(`[KwaiPilot] restoreChunkOriginalCode: Failed to write file: ${error}`);
			return undefined;
		}
	}

	private removeChunkDecoration(uri: URI, editor: ICodeEditor, chunkId: string): void {
		const model = editor.getModel();
		if (!model) {
			return;
		}
		const state = this.streamingStates.get(model.uri.path.toString());
		if (!state) {
			return;
		}
		const chunkInfo = this.floatingChunkWidgets.find(widget => widget.chunkId === chunkId);
		const diffEntry = chunkInfo?.diffEntry;
		if (!chunkInfo || !diffEntry) {
			return;
		}
		const diffAreas = this.getDiffAreas(uri);
		// 移除 diffAreas 里的 chunk
		if (diffAreas.length > 0) {
			const area = diffAreas[0];
			const idx = area.diffs.findIndex(d => this.isSameDiffEntry(d.change, diffEntry));
			if (idx !== -1) {
				area.diffs.splice(idx, 1);
				if (area.diffs.length === 0) {
					this.diffAreas.delete(area.id);
				}
			}
		}
		const rmDecorationIndex = this.decorationCollection.findIndex(d => this.isSameDiffEntry(d.diffEntry, diffEntry));
		const rmDecoration = this.decorationCollection[rmDecorationIndex];
		// 1. 移除 model decoration
		if (rmDecoration?.decorationIds && rmDecoration.decorationIds.length) {
			model.deltaDecorations(rmDecoration?.decorationIds?.map(String), []);
			rmDecoration.decorationIds = [];
		}
		if (rmDecorationIndex !== -1) {
			this.decorationCollection.splice(rmDecorationIndex, 1);
		}

		// 2. 移除所有相关的装饰器（包括行级装饰器）
		const allDecorations = model.getAllDecorations();
		const decorationsToRemove = allDecorations.filter(d => {
			// 只删除我们创建的装饰器
			const description = d.options.description;
			return /kwaipilot/.test(description) || /TextEditorDecorationType/.test(d.options.className || '');
		});

		if (decorationsToRemove.length > 0) {
			model.deltaDecorations(decorationsToRemove.map(d => d.id), []);
		}

		// 3. 移除 view zones
		if (chunkInfo.viewZoneId) {
			editor.changeViewZones(accessor => {
				accessor.removeZone(String(chunkInfo.viewZoneId));
				// 检查 state 中的 viewZones
				const state = this.streamingStates.get(model.uri.path.toString());
				if (state) {
					state.viewZones.forEach(id => {
						if (id === chunkInfo.viewZoneId) {
							accessor.removeZone(id);
						}
					});
					state.viewZones.splice(state.viewZones.indexOf(chunkInfo.viewZoneId || ''), 1);
				}
			});
		}
		if (chunkInfo.widget) {
			editor.removeContentWidget(chunkInfo.widget);
		}
		if (chunkInfo.domNode && chunkInfo.domNode.parentElement) {
			chunkInfo.domNode.parentElement.removeChild(chunkInfo.domNode);
		}
		const idx = this.floatingChunkWidgets.findIndex(w => w.chunkId === chunkId);
		if (idx !== -1) {
			this.floatingChunkWidgets.splice(idx, 1);
		}
	}


	/** 代码块操作后，判断当前文件是否是全部接受/全部拒绝/部分拒绝 */
	private updateExtensionStatusAfterChunkOperate(uri: URI) {
		const rejectChunkIds = this.chunkRejectStates.get(uri.path.toString()) || [];
		const acceptChunkIds = this.chunkAcceptStates.get(uri.path.toString()) || [];
		if (rejectChunkIds.length > 0 && acceptChunkIds.length === 0) {
			// 全部拒绝
			this.kwaiPilotBridgeAPIService.editor.undoDiff({ filepath: this.getRelativePathForFile(uri) });
		} else if (rejectChunkIds.length > 0) {
			// 部分拒绝
			const filepath = this.getRelativePathForFile(uri);
			this.kwaiPilotBridgeAPIService.editor.keepDiff({ filepath, filesStatus: { [filepath]: 'rejected' } });
		} else {
			// 全部接受
			this.kwaiPilotBridgeAPIService.editor.keepDiff({ filepath: this.getRelativePathForFile(uri) });
		}
	}

	/** 文件操作后，判断当前文件是否是全部接受/全部拒绝/部分拒绝 */
	private updateExtensionStatusAfterFileOperate(uri: URI, operate: 'accept' | 'reject') {
		const rejectChunkIds = this.chunkRejectStates.get(uri.path.toString()) || [];
		const acceptChunkIds = this.chunkAcceptStates.get(uri.path.toString()) || [];
		// 操作了文件的拒绝，需要看是否已经接受过该文件的部分代码块
		if (operate === 'reject') {
			if (acceptChunkIds.length > 0) {
				// 部分拒绝
				const filepath = this.getRelativePathForFile(uri);
				this.kwaiPilotBridgeAPIService.editor.keepDiff({ filepath, filesStatus: { [filepath]: 'rejected' } });
			} else {
				this.kwaiPilotBridgeAPIService.editor.undoDiff({ filepath: this.getRelativePathForFile(uri) });
			}
		} else {
			// 操作了文件的接受，需要看是否已经拒绝过该文件的部分代码块
			if (rejectChunkIds.length > 0) {
				// 部分拒绝
				const filepath = this.getRelativePathForFile(uri);
				this.kwaiPilotBridgeAPIService.editor.keepDiff({ filepath, filesStatus: { [filepath]: 'rejected' } });
			} else {
				this.kwaiPilotBridgeAPIService.editor.keepDiff({ filepath: this.getRelativePathForFile(uri) });
			}
		}
	}

	/** 每一个 chunk 都被 accept/reject */
	private isAllChunksHandled(uri: URI): boolean {
		const chunks = this.floatingChunkWidgets;
		if (chunks.length === 0) {
			this.updateExtensionStatusAfterChunkOperate(uri);
			return true;
		}
		return false;

	}
	/** 清除指定 URI 的 chunk 接受状态 */
	private clearChunkAcceptStates(uri: URI) {
		this.chunkAcceptStates.delete(uri.path.toString());
		this.chunkRejectStates.delete(uri.path.toString());
	}

	/** 移除 diff footer 菜单 */
	private removeDiffFooter() {
		if (this.diffFooterDom && this.diffFooterDom.parentElement) {
			this.diffFooterDom.parentElement.removeChild(this.diffFooterDom);
			this.diffFooterDom = undefined;
		}
		this.currentDiffIndex = 0;
	}

	/** 流式更新时禁止编辑 */
	private setStreamingMask(editor: ICodeEditor, show: boolean) {
		if (show) {
			editor.updateOptions({ readOnly: true });
		} else {
			editor.updateOptions({ readOnly: false });
		}
	}

	/**
	 * 获取文件相对于工作区的路径
	 * @param uri 文件URI
	 * @returns 相对路径字符串，如果无法获取则返回完整路径
	 */
	getRelativePathForFile(uri: URI): string {
		const root = this.workspaceContextService.getWorkspaceFolder(uri);
		if (root) {
			// 直接使用工作区根目录计算相对路径，而不是父目录
			return getRelativePath(root.uri, uri) ?? uri.path;
		}
		// 如果没有工作区，返回文件路径
		return uri.path;
	}

}

/**
 * 注册"接受 Diff"快捷键和命令
 */
KeybindingsRegistry.registerCommandAndKeybindingRule({
	id: 'kwaipilot.keybindings.acceptDiff',
	weight: KeybindingWeight.ExternalExtension + 200,
	when: ContextKeyExpr.has('kwaipilot.diffMode'),
	primary: KeyMod.CtrlCmd | KeyCode.Enter,
	handler: (accessor, args) => {
		const editorService = accessor.get(IEditorService);
		const editCodeService = accessor.get(IEditCodeService);
		const editor = editorService.activeTextEditorControl as ICodeEditor;
		const model = editor?.getModel();
		if (model) {
			// 只传 uri，range 可选
			editCodeService.acceptDiff({ uri: model.uri });
		}
	}
});

/**
 * 注册"拒绝 Diff"快捷键和命令
 */
KeybindingsRegistry.registerCommandAndKeybindingRule({
	id: 'kwaipilot.keybindings.rejectDiff',
	weight: KeybindingWeight.ExternalExtension + 200,
	when: ContextKeyExpr.has('kwaipilot.diffMode'),
	primary: KeyMod.Shift | KeyMod.CtrlCmd | KeyCode.Backspace,
	handler: (accessor, args) => {
		const editorService = accessor.get(IEditorService);
		const editCodeService = accessor.get(IEditCodeService);
		const editor = editorService.activeTextEditorControl as ICodeEditor;
		const model = editor?.getModel();
		if (model) {
			// 只传 uri，range 可选
			editCodeService.rejectDiff({ uri: model.uri });
		}
	}
});

/**
 * 注册"接受当前代码块"快捷键和命令
 */
KeybindingsRegistry.registerCommandAndKeybindingRule({
	id: 'kwaipilot.keybindings.acceptCurrentChunk',
	weight: KeybindingWeight.ExternalExtension + 200,
	when: ContextKeyExpr.has('kwaipilot.diffMode'),
	primary: KeyMod.CtrlCmd | KeyCode.KeyY,
	handler: (accessor, args) => {
		const editorService = accessor.get(IEditorService);
		const editCodeService = accessor.get(IEditCodeService);
		const editor = editorService.activeTextEditorControl as ICodeEditor;
		const model = editor?.getModel();
		if (model) {
			// 直接调用接口
			editCodeService.acceptCurrentChunk(editor, model.uri);
		}
	}
});

/**
 * 注册"拒绝当前代码块"快捷键和命令
 */
KeybindingsRegistry.registerCommandAndKeybindingRule({
	id: 'kwaipilot.keybindings.rejectCurrentChunk',
	weight: KeybindingWeight.ExternalExtension + 200,
	when: ContextKeyExpr.has('kwaipilot.diffMode'),
	primary: KeyMod.CtrlCmd | KeyCode.KeyN,
	handler: (accessor, args) => {
		const editorService = accessor.get(IEditorService);
		const editCodeService = accessor.get(IEditCodeService);
		const editor = editorService.activeTextEditorControl as ICodeEditor;
		const model = editor?.getModel();
		if (model) {
			// 直接调用接口
			editCodeService.rejectCurrentChunk(editor, model.uri);
		}
	}
});

// 保留原有的内部命令注册（如有需要）
CommandsRegistry.registerCommand({
	id: 'kwaipilot.acceptCurrentChunkInternal',
	handler: (accessor, editor: ICodeEditor, uri: URI) => {
		const editCodeService = accessor.get(IEditCodeService);
		editCodeService.acceptCurrentChunk(editor, uri);
	}
});
CommandsRegistry.registerCommand({
	id: 'kwaipilot.rejectCurrentChunkInternal',
	handler: (accessor, editor: ICodeEditor, uri: URI) => {
		const editCodeService = accessor.get(IEditCodeService);
		editCodeService.rejectCurrentChunk(editor, uri);
	}
});


// Register service
registerSingleton(IEditCodeService, EditCodeService, InstantiationType.Delayed);
