import { registerAction2, Action2 } from '../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import { IEditCodeService } from '../common/editCode.js';
import { URI } from '../../../../base/common/uri.js';

// 命令常量
export const enum KwaipilotCommands {
	// IDE -> Plugin
	ShowDiff = 'kwaipilot.editCode.showDiff',
	AcceptDiff = 'kwaipilot.editCode.acceptDiff',
	RejectDiff = 'kwaipilot.editCode.rejectDiff',
	UpdateDiff = 'kwaipilot.editCode.updateDiff',
	AcceptAllDiff = 'kwaipilot.editCode.acceptAllDiff',
	RejectAllDiff = 'kwaipilot.editCode.rejectAllDiff',

	// Plugin -> IDE
	KeepDiff = 'kwaipilot.editCode.keepDiff',
	UndoDiff = 'kwaipilot.editCode.undoDiff',
	RequestShowDiff = 'kwaipilot.editCode.requestShowDiff',
	ClearAllDiffState = 'kwaipilot.editCode.clearAllDiffState'
}

// 注册 ShowDiff 命令
registerAction2(class ShowDiffAction extends Action2 {
	constructor() {
		super({
			id: KwaipilotCommands.ShowDiff,
			title: 'Show Diff'
		});
	}

	async run(accessor: ServicesAccessor, params: { uri: string; originalContent: string; newContent: string }) {
		const { uri, originalContent, newContent } = params;
		const editCodeService = accessor.get(IEditCodeService);

		if (typeof uri !== 'string') {
			console.error('ShowDiffAction: Invalid fsPath', uri);
			return;
		}

		await editCodeService.showDiff({
			uri: URI.file(uri),
			originalContent: originalContent || '',
			modifiedContent: newContent || ''
		});
	}
});

// 注册 AcceptDiff 命令
registerAction2(class AcceptDiffAction extends Action2 {
	constructor() {
		super({
			id: KwaipilotCommands.AcceptDiff,
			title: 'Accept Diff'
		});
	}

	async run(accessor: ServicesAccessor, args: { uri: string; range: { startLineNumber: number; startColumn: number; endLineNumber: number; endColumn: number } }) {
		const editCodeService = accessor.get(IEditCodeService);
		await editCodeService.acceptDiff({
			uri: URI.parse(args.uri),
			range: args.range
		});
	}
});

// 注册 RejectDiff 命令
registerAction2(class RejectDiffAction extends Action2 {
	constructor() {
		super({
			id: KwaipilotCommands.RejectDiff,
			title: 'Reject Diff'
		});
	}

	async run(accessor: ServicesAccessor, args: { uri: string; range: { startLineNumber: number; startColumn: number; endLineNumber: number; endColumn: number } }) {
		const editCodeService = accessor.get(IEditCodeService);
		await editCodeService.rejectDiff({
			uri: URI.parse(args.uri),
			range: args.range
		});
	}
});

// 注册 UpdateDiff 命令
registerAction2(class UpdateDiffAction extends Action2 {
	constructor() {
		super({
			id: KwaipilotCommands.UpdateDiff,
			title: 'Update Diff'
		});
	}

	/** content 是全量新内容  */
	async run(accessor: ServicesAccessor, params: { uri: string; content: string; isFinal: boolean }) {
		const { uri, content, isFinal } = params;
		const editCodeService = accessor.get(IEditCodeService);
		await editCodeService.updateDiff({
			uri: URI.file(uri),
			content,
			isFinal
		});
	}
});

// 注册 AcceptAllDiff 命令
registerAction2(class AcceptAllDiffAction extends Action2 {
	constructor() {
		super({
			id: KwaipilotCommands.AcceptAllDiff,
			title: 'Accept All Diffs'
		});
	}

	async run(accessor: ServicesAccessor) {
		const editCodeService = accessor.get(IEditCodeService);
		await editCodeService.acceptAllDiffs();
	}
});
// 注册 RejectAllDiff 命令
registerAction2(class RejectAllDiffAction extends Action2 {
	constructor() {
		super({
			id: KwaipilotCommands.RejectAllDiff,
			title: 'Reject All Diffs'
		});
	}

	async run(accessor: ServicesAccessor) {
		const editCodeService = accessor.get(IEditCodeService);
		await editCodeService.rejectAllDiffs();
	}
});
