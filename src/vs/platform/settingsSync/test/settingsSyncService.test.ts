/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as assert from 'assert';
import { DisposableStore } from '../../../base/common/lifecycle.js';
import { TestInstantiationService } from '../../../platform/instantiation/test/common/instantiationServiceMock.js';
import { ILogService, NullLogService } from '../../../platform/log/common/log.js';
import { IFileService } from '../../../platform/files/common/files.js';
import { FileService } from '../../../platform/files/common/fileService.js';
import { InMemoryFileSystemProvider } from '../../../platform/files/common/inMemoryFileSystemProvider.js';
import { Schemas } from '../../../base/common/network.js';
import { URI } from '../../../base/common/uri.js';
import { IEnvironmentService } from '../../../platform/environment/common/environment.js';
import { mock } from '../../../base/test/common/mock.js';
import { SettingsSyncService } from '../common/settingsSyncService.js';
import { ISettingsSyncProgressService } from '../common/settingsSyncProgress.js';
import { IGlobalExtensionEnablementService, IExtensionIdentifier } from '../../../platform/extensionManagement/common/extensionManagement.js';
import { VSCodeIdeType } from '../../../platform/environment/common/environmentService.js';

suite('SettingsSyncService - Disabled Extensions Import', () => {
	let disposables: DisposableStore;
	let instantiationService: TestInstantiationService;
	let settingsSyncService: SettingsSyncService;
	let mockGlobalExtensionEnablementService: IGlobalExtensionEnablementService;
	let mockEnvironmentService: IEnvironmentService;
	let mockProgressService: ISettingsSyncProgressService;

	setup(() => {
		disposables = new DisposableStore();
		instantiationService = new TestInstantiationService();

		// Setup file service
		const fileService = new FileService(new NullLogService());
		const fileSystemProvider = new InMemoryFileSystemProvider();
		fileService.registerProvider(Schemas.file, fileSystemProvider);
		instantiationService.stub(IFileService, fileService);
		instantiationService.stub(ILogService, new NullLogService());

		// Mock environment service
		mockEnvironmentService = new class extends mock<IEnvironmentService>() {
			override userRoamingDataHome = URI.file('/test/user');
		};
		instantiationService.stub(IEnvironmentService, mockEnvironmentService);

		// Mock progress service
		mockProgressService = new class extends mock<ISettingsSyncProgressService>() {
			override report() { }
		};
		instantiationService.stub(ISettingsSyncProgressService, mockProgressService);

		// Mock global extension enablement service
		const mockDisabledExtensions: IExtensionIdentifier[] = [];
		mockGlobalExtensionEnablementService = new class extends mock<IGlobalExtensionEnablementService>() {
			override getDisabledExtensions() {
				return mockDisabledExtensions;
			}
			override async disableExtension(extension: IExtensionIdentifier, source?: string) {
				mockDisabledExtensions.push(extension);
				return true;
			}
			override async enableExtension(extension: IExtensionIdentifier, source?: string) {
				const index = mockDisabledExtensions.findIndex(ext => ext.id === extension.id);
				if (index !== -1) {
					mockDisabledExtensions.splice(index, 1);
					return true;
				}
				return false;
			}
		};
		instantiationService.stub(IGlobalExtensionEnablementService, mockGlobalExtensionEnablementService);

		settingsSyncService = instantiationService.createInstance(SettingsSyncService);
	});

	teardown(() => {
		disposables.dispose();
	});

	test('should have importDisabledExtensions method', () => {
		assert.ok(settingsSyncService);
		assert.ok(typeof settingsSyncService.importDisabledExtensions === 'function');
	});

	test('should handle missing storage database gracefully', async () => {
		// Test importing from a non-existent source
		const result = await settingsSyncService.importDisabledExtensions('Code' as VSCodeIdeType);
		
		// Should return true even if no database exists
		assert.strictEqual(result, true);
	});

	test('should use correct AMD module import for sqlite3', () => {
		// This test verifies that the service can be instantiated without import errors
		// The actual sqlite3 import will be tested when the method is called
		assert.ok(settingsSyncService);
		
		// Verify the method exists and is callable
		assert.ok(typeof settingsSyncService.importDisabledExtensions === 'function');
	});

	test('should not duplicate already disabled extensions', async () => {
		// Pre-disable an extension
		await mockGlobalExtensionEnablementService.disableExtension({ id: 'test.extension' });
		
		const initialCount = mockGlobalExtensionEnablementService.getDisabledExtensions().length;
		assert.strictEqual(initialCount, 1);
		
		// The actual import would need a mock database, but we can test the service structure
		assert.ok(settingsSyncService.importDisabledExtensions);
	});

	test('should handle invalid source IDE gracefully', async () => {
		// Test with a valid but non-existent IDE
		const result = await settingsSyncService.importDisabledExtensions('Cursor' as VSCodeIdeType);
		assert.strictEqual(result, true);
	});
});

/**
 * Mock helper for testing
 */
export class MockExtensionEnablementService {
	private disabledExtensions: IExtensionIdentifier[] = [];

	getDisabledExtensions(): IExtensionIdentifier[] {
		return [...this.disabledExtensions];
	}

	async disableExtension(extension: IExtensionIdentifier, source?: string): Promise<boolean> {
		const exists = this.disabledExtensions.some(ext => ext.id === extension.id);
		if (!exists) {
			this.disabledExtensions.push(extension);
		}
		return true;
	}

	async enableExtension(extension: IExtensionIdentifier, source?: string): Promise<boolean> {
		const index = this.disabledExtensions.findIndex(ext => ext.id === extension.id);
		if (index !== -1) {
			this.disabledExtensions.splice(index, 1);
			return true;
		}
		return false;
	}

	reset(): void {
		this.disabledExtensions = [];
	}
}
