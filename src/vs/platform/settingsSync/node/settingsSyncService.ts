/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { URI } from '../../../base/common/uri.js';
import { ILogService } from '../../log/common/log.js';
import { IFileService } from '../../files/common/files.js';
import { VSCodeIdeType, VSCodePathUtils } from '../../environment/common/environmentService.js';
import { IExtensionIdentifier } from '../../extensionManagement/common/extensionManagement.js';
import { importAMDNodeModule } from '../../../amdX.js';

/**
 * Node.js specific implementation for reading disabled extensions from SQLite database
 */
export class NodeSettingsSyncService {

	constructor(
		private readonly logService: ILogService,
		private readonly fileService: IFileService
	) { }

	/**
	 * 从源IDE的SQLite存储数据库中读取禁用插件状态
	 * 这个方法只在Node.js环境中可用
	 */
	async readDisabledExtensionsFromStorage(name: VSCodeIdeType): Promise<IExtensionIdentifier[]> {
		try {
			const pathUtils = new VSCodePathUtils(name);
			// 构建存储数据库路径: {用户数据目录}/User/globalStorage/state.vscdb
			const userDataPath = pathUtils.getUserDataPath();
			const storageDbPath = `${userDataPath}/User/globalStorage/state.vscdb`;

			// 检查存储数据库文件是否存在
			const storageDbUri = URI.file(storageDbPath);
			const exists = await this.fileService.exists(storageDbUri);

			if (!exists) {
				this.logService.info(`Storage database not found at: ${storageDbPath}`);
				return [];
			}

			// 动态导入 sqlite3 (只在Node.js环境中可用)
			const sqlite3 = await importAMDNodeModule<typeof import('@vscode/sqlite3')>('@vscode/sqlite3', 'lib/sqlite3.js');
			const Database = sqlite3.Database;

			return new Promise<IExtensionIdentifier[]>((resolve, reject) => {
				const db = new Database(storageDbPath, (err: any) => {
					if (err) {
						reject(new Error(`Failed to open database: ${err.message}`));
						return;
					}

					// 查询禁用插件数据
					const query = 'SELECT value FROM ItemTable WHERE key = ?';
					const storageKey = 'extensionsIdentifiers/disabled';

					db.get(query, [storageKey], (err: any, row: any) => {
						if (err) {
							db.close();
							reject(new Error(`Database query failed: ${err.message}`));
							return;
						}

						db.close();

						if (!row || !row.value) {
							resolve([]);
							return;
						}

						try {
							const disabledExtensions = JSON.parse(row.value);
							if (Array.isArray(disabledExtensions)) {
								resolve(disabledExtensions);
							} else {
								resolve([]);
							}
						} catch (parseError) {
							this.logService.error('Failed to parse disabled extensions data:', parseError);
							resolve([]);
						}
					});
				});
			});
		} catch (error) {
			this.logService.error('Error reading disabled extensions from storage:', error);
			return [];
		}
	}
}
