/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { parse as parseJson } from '../../../base/common/json.js';
import { ILogService } from '../../../platform/log/common/log.js';
import { IEnvironmentService } from '../../../platform/environment/common/environment.js';
// import { SettingsMapper } from './settingsMapper.js';
import { localize } from '../../../nls.js';
import { ISettingsSyncProgressService, SettingsSyncPhase } from './settingsSyncProgress.js';
import { SettingsSyncError, SettingsSyncErrorCode, ISettingsSyncService, IDisabledExtensionsReaderService, IExtensionIdentifier } from './settingsSync.js';
import { URI } from '../../../base/common/uri.js';
import { IFileService } from '../../../platform/files/common/files.js';
import { VSBuffer } from '../../../base/common/buffer.js';
import { VSCodeIdeType, VSCodePathUtils } from '../../environment/common/environmentService.js';
import { IGlobalExtensionEnablementService } from '../../extensionManagement/common/extensionManagement.js';

export class SettingsSyncService implements ISettingsSyncService {
	declare readonly _serviceBrand: undefined;

	// private readonly settingsMapper: SettingsMapper;

	constructor(
		@ILogService private readonly logService: ILogService,
		@IEnvironmentService private readonly environmentService: IEnvironmentService,
		@ISettingsSyncProgressService private readonly progressService: ISettingsSyncProgressService,
		@IFileService private readonly fileService: IFileService,
		@IGlobalExtensionEnablementService private readonly globalExtensionEnablementService: IGlobalExtensionEnablementService,
		@IDisabledExtensionsReaderService private readonly disabledExtensionsReaderService: IDisabledExtensionsReaderService
	) {
		// this.settingsMapper = new SettingsMapper(this.logService);
	}

	async readUserSettings(): Promise<string> {
		try {
			const settingsFile = URI.joinPath(this.environmentService.userRoamingDataHome, 'User', 'settings.json');
			const content = await this.fileService.readFile(settingsFile);
			return content.value.toString();
		} catch (error) {
			this.logService.error('Failed to read user settings:', error);
			throw new SettingsSyncError(
				SettingsSyncErrorCode.FileNotFound,
				'Failed to read settings file',
				error as Error
			);
		}
	}

	async validateSettings(content: string): Promise<boolean> {
		try {
			parseJson(content);
			return true;
		} catch (error) {
			return false;
		}
	}

	/**
	 * Import user settings and keyboard bindings from target IDE (VSCode family)
	 * @param name
	 * @returns
	 */
	async importSettings(name: VSCodeIdeType): Promise<boolean> {
		try {
			this.progressService.report({
				phase: SettingsSyncPhase.Reading,
				message: localize('reading', "Reading settings...")
			});

			const pathUtils = new VSCodePathUtils(name);

			const readFileContent = async (filePath: URI, defaultValue: string): Promise<string> => {
				try {
					const content = await this.fileService.readFile(filePath);
					return content.value.toString();
				} catch (error) {
					this.logService.info(`No existing file found at ${filePath.path}`);
					return defaultValue;
				}
			};

			// Get the current user settings path and keybindings path
			const userSettingsFile = URI.joinPath(this.environmentService.userRoamingDataHome, 'settings.json');
			const keybindingsFile = URI.joinPath(this.environmentService.userRoamingDataHome, 'keybindings.json');

			// const currentUserSettings = await readFileContent(userSettingsFile, '{}');
			// const currentKeybindings = await readFileContent(keybindingsFile, '[]');

			// Get the user settings path and keybindings path of vscode
			const targetUserSettingsFile = URI.file(pathUtils.getUserSettingsPath());
			const targetKeybindingsFile = URI.file(pathUtils.getKeybindingsPath());

			const targetUserSettings = await readFileContent(targetUserSettingsFile, '{}');
			const targetKeybindings = await readFileContent(targetKeybindingsFile, '[]');

			this.progressService.report({
				phase: SettingsSyncPhase.Writing,
				message: localize('writing', "Writing settings...")
			});

			// Write new settings
			await this.fileService.writeFile(userSettingsFile, VSBuffer.fromString(targetUserSettings));
			await this.fileService.writeFile(keybindingsFile, VSBuffer.fromString(targetKeybindings));

			this.progressService.report({
				phase: SettingsSyncPhase.Done,
				message: localize('done', "Settings imported successfully")
			});

			this.logService.info('Settings imported successfully');

			return true;
		} catch (error) {
			this.logService.error('Failed to import settings:', error);
			throw new SettingsSyncError(
				SettingsSyncErrorCode.UnknownError,
				'Failed to import settings',
				error as Error
			);
		}
	}

	/**
	 * 导入 mcp 设置文件
	 */
	async importMcpSettings(name: VSCodeIdeType): Promise<boolean> {
		const pathUtils = new VSCodePathUtils(name);
		// 获取当前 ide mcp 路径 /Users/<USER>/.kwaipilot/mcp/kwaipilot-mcp-settings.json
		const currentMcpPath = URI.joinPath(URI.file(pathUtils.getHomeDir()), '.kwaipilot', 'mcp', 'kwaipilot-mcp-settings.json');
		// 获取 目标 ide mcp 路径
		const targetMcpPath = URI.file(pathUtils.getMcpSettingsPath());


		// 读取，覆盖
		try {
			const content = await this.fileService.readFile(targetMcpPath);
			const output = this.transform2UnionMcpSettings(name, JSON.parse(content.value.toString()));
			await this.fileService.writeFile(currentMcpPath, VSBuffer.fromString(JSON.stringify(output, null, 2)));
			return true;
		} catch (error) {
			this.logService.error('Failed to import mcp settings:', error);
		}
		return true;
	}

	/**
	 * 导入禁用插件状态
	 * 从源IDE的存储数据库中读取禁用插件信息，并应用到当前IDE
	 */
	async importDisabledExtensions(name: VSCodeIdeType): Promise<boolean> {
		try {
			this.progressService.report({
				phase: SettingsSyncPhase.Reading,
				message: localize('readingDisabledExtensions', "Reading disabled extensions...")
			});

			// 读取源IDE的禁用插件状态
			const sourceDisabledExtensions = await this.readDisabledExtensionsFromSourceIDE(name);

			if (sourceDisabledExtensions && sourceDisabledExtensions.length > 0) {
				this.progressService.report({
					phase: SettingsSyncPhase.Writing,
					message: localize('applyingDisabledExtensions', "Applying disabled extensions...")
				});

				// 应用禁用插件状态到当前IDE
				await this.applyDisabledExtensions(sourceDisabledExtensions);

				this.progressService.report({
					phase: SettingsSyncPhase.Done,
					message: localize('disabledExtensionsImported', "Disabled extensions imported successfully")
				});

				this.logService.info(`Successfully imported ${sourceDisabledExtensions.length} disabled extensions from ${name}`);
			} else {
				this.logService.info(`No disabled extensions found in ${name}`);
			}

			return true;
		} catch (error) {
			this.logService.error('Failed to import disabled extensions:', error);
			throw new SettingsSyncError(
				SettingsSyncErrorCode.UnknownError,
				'Failed to import disabled extensions',
				error as Error
			);
		}
	}

	/**
	 * 从源IDE读取禁用插件状态
	 * 通过IPC通道调用Node.js服务来读取SQLite数据库
	 */
	private async readDisabledExtensionsFromSourceIDE(name: VSCodeIdeType): Promise<IExtensionIdentifier[]> {
		try {
			// 通过IPC通道调用Node.js服务
			const disabledExtensions = await this.disabledExtensionsReaderService.readDisabledExtensions(name);
			this.logService.info(`Found ${disabledExtensions.length} disabled extensions from ${name}`);
			return disabledExtensions;

		} catch (error) {
			this.logService.error(`Error reading disabled extensions from ${name}:`, error);
			this.logService.info(`Users can manually disable extensions after import if needed`);
			return [];
		}
	}

	/**
	 * 应用禁用插件状态到当前IDE
	 */
	private async applyDisabledExtensions(disabledExtensions: IExtensionIdentifier[]): Promise<void> {
		try {
			// 获取当前已禁用的插件
			const currentDisabledExtensions = this.globalExtensionEnablementService.getDisabledExtensions();

			// 创建一个Set来快速查找已禁用的插件
			const currentDisabledSet = new Set(currentDisabledExtensions.map(ext => ext.id.toLowerCase()));

			// 禁用从源IDE导入的插件
			for (const extension of disabledExtensions) {
				if (!currentDisabledSet.has(extension.id.toLowerCase())) {
					try {
						await this.globalExtensionEnablementService.disableExtension(extension, 'import');
						this.logService.info(`Disabled extension: ${extension.id}`);
					} catch (error) {
						this.logService.warn(`Failed to disable extension ${extension.id}:`, error);
					}
				} else {
					this.logService.info(`Extension ${extension.id} is already disabled`);
				}
			}
		} catch (error) {
			this.logService.error('Error applying disabled extensions:', error);
			throw error;
		}
	}

	private transform2UnionMcpSettings(name: VSCodeIdeType, settings: Record<string, any>) {
		// VSCode copilot 的 mcp 配置文件示例
		// {
		// 	"mcp": {
		// 		"servers": {
		// 			"sequential-thinking": {
		// 				"command": "npx",
		// 					"args": [
		// 						"-y",
		// 						"@modelcontextprotocol/server-sequential-thinking"
		// 					]
		// 			}
		// 		}
		// 	},
		// }

		// 标准的 mcp 配置文件示例
		// {
		// 	"mcpServers": {
		// 	  "sequential-thinking": {
		// 		"command": "npx",
		// 		"args": [
		// 		  "-y",
		// 		  "@modelcontextprotocol/server-sequential-thinking"
		// 		]
		// 	  }
		// 	}
		//   }
		// Code 的需要特殊处理
		if (name === 'Code') {
			return {
				'mcpServers': settings?.mcp?.servers
			};
		}
		return settings;
	}
}
